# SmartAdmin 开发经验总结

## 📋 概述

本文档汇总了 SmartAdmin 项目开发过程中的重要经验、最佳实践和问题解决方案，为开发团队提供参考。

## 🎯 前端开发经验

### Vue3 组件开发

#### 表单组件改造经验
以任务表单（task-form.vue）为例，总结了组件改造的完整流程：

**需求背景**：
- 接收者字段：从输入框改为下拉选择
- 发布者字段：自动填充当前用户，不可编辑

**实现步骤**：

1. **组件替换**
```vue
<!-- 原始输入框 -->
<a-input v-model:value="form.receiver" placeholder="请输入接收者" />

<!-- 改为下拉选择 -->
<a-select
  v-model:value="form.receiver"
  placeholder="请选择接收者"
  show-search
  :filter-option="filterOption"
  style="width: 100%"
>
  <a-select-option v-for="option in receiverOptions" :key="option.value" :value="option.value">
    {{ option.label }}
  </a-select-option>
</a-select>
```

2. **异步数据加载**
```javascript
import { employeeApi } from '/@/api/system/employee-api';

const receiverOptions = ref([]);

async function loadReceiverOptions() {
  try {
    const res = await employeeApi.queryAll();
    if (res.data) {
      receiverOptions.value = res.data.map(employee => ({
        value: employee.actualName,
        label: employee.actualName,
      }));
    }
  } catch (err) {
    console.error("Failed to load receivers:", err);
    smartSentry.captureError(err);
  }
}
```

3. **搜索功能实现**
```javascript
const filterOption = (input, option) => {
  return option.label.toLowerCase().includes(input.toLowerCase());
};
```

#### 用户状态管理
**自动填充当前用户信息**：
```javascript
import { useUserStore } from '/@/store/modules/user-store';

const userStore = useUserStore();

// 在表单初始化时自动填充
function initForm() {
  form.value.publisher = userStore.userInfo.actualName;
}
```

### 前端样式问题解决经验

#### 输入框边框显示问题
**问题场景**: 输入框右边框被截断，显示不完整

**常见原因**:
1. 容器右边距不足
2. 复杂的CSS样式覆盖
3. 绝对定位元素影响布局

**解决原则**:
```scss
// ❌ 错误做法：过度复杂的样式
&.input-item {
    margin-right: 40px;
    padding-right: 5px;

    :deep(.ant-input) {
        width: 150px !important;
        box-sizing: border-box;
        // ... 更多复杂样式
    }
}

// ✅ 正确做法：简单有效的解决方案
&.input-item {
    margin-right: 30px; // 适当增加右边距即可
}
```

**最佳实践**:
1. **先分析后修改**: 完整查看相关代码结构，理解问题根源
2. **简单优先**: 优先使用最简单的解决方案
3. **避免过度设计**: 不要引入不必要的复杂样式
4. **保持一致性**: 与其他类似组件的样式保持一致

### 组件复用与扩展经验

#### 回购ID功能开发经验
以链接视角页面回购ID功能为例，总结了功能扩展的完整实现：

**需求背景**：
- 在现有货品ID筛选基础上，新增回购ID选择功能
- 实现精确复购分析：分析购买货品A的复购客户对货品B的复购情况

**实现策略**：

1. **前端组件复用**
```javascript
// 复用现有的ProductSelectModal组件
const repurchaseProductSelectModalRef = ref();
const selectedRepurchaseProducts = ref([]);

// 创建独立的回购商品选择方法
function showRepurchaseProductSelectModal() {
    repurchaseProductSelectModalRef.value.showModal(queryForm.selectedRepurchaseProductIds);
}

function onRepurchaseProductSelected(products) {
    selectedRepurchaseProducts.value = products;
    queryForm.selectedRepurchaseProductIds = products.map(p => p.goodsId);
    message.success(`已选择 ${products.length} 个回购ID`);
}
```

2. **后端参数扩展**
```java
// 在QueryForm中新增回购ID字段
public class CustomerLinkQueryForm {
    private List<String> platformGoodsIds;         // 原有货品ID
    private List<String> repurchasePlatformGoodsIds; // 新增回购ID
}
```

3. **SQL逻辑设计**
```xml
<!-- 使用choose-when-otherwise实现条件逻辑 -->
<choose>
    <when test="queryForm.repurchasePlatformGoodsIds != null and queryForm.repurchasePlatformGoodsIds.size() > 0">
        AND om.平台货品ID IN
        <foreach collection="queryForm.repurchasePlatformGoodsIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </when>
    <otherwise>
        <if test="queryForm.platformGoodsIds != null and queryForm.platformGoodsIds.size() > 0">
            AND om.平台货品ID IN
            <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </otherwise>
</choose>
```

**核心经验**：
- **组件复用**: 通过ref创建独立实例，避免状态冲突
- **向下兼容**: 新功能不影响原有逻辑，保持系统稳定性
- **优先级设计**: 回购ID优先于货品ID，提供更精确的分析
- **数据一致性**: 确保分析数据、复购明细、下载明细使用一致的筛选逻辑

#### UI优化开发经验

**1. 缓存机制实现**
```javascript
// 24小时本地缓存机制
const CACHE_KEY = 'product_select_modal_cache';
const CACHE_EXPIRE_TIME = 24 * 60 * 60 * 1000;

function getCachedData() {
    try {
        const cached = localStorage.getItem(CACHE_KEY);
        if (cached) {
            const data = JSON.parse(cached);
            const now = Date.now();
            if (now - data.timestamp < CACHE_EXPIRE_TIME) {
                return data.result;
            } else {
                localStorage.removeItem(CACHE_KEY);
            }
        }
    } catch (e) {
        console.warn('读取缓存失败:', e);
        localStorage.removeItem(CACHE_KEY);
    }
    return null;
}
```

**2. 独立清除功能**
```vue
<!-- 输入框添加清除按钮 -->
<template #suffix>
    <span style="display: flex; align-items: center; gap: 4px;">
        <CloseOutlined
            v-if="selectedProducts.length > 0"
            @click.stop="clearProductSelection"
            style="cursor: pointer; color: #999; font-size: 12px;"
            title="清除选择"
        />
        <SelectOutlined />
    </span>
</template>
```

**3. 默认选择优化**
```javascript
// 支持"全部货品"默认选择
function onConfirm() {
    if (selectAllProducts.value) {
        emit('confirm', [{ goodsId: 'ALL_PRODUCTS', shopName: '全部货品', displayName: '全部货品' }]);
    } else if (selectedRows.value.length === 0) {
        // 没有选择时默认为全部货品
        emit('confirm', [{ goodsId: 'ALL_PRODUCTS', shopName: '全部货品', displayName: '全部货品' }]);
    } else {
        emit('confirm', selectedRows.value);
    }
}
```

**4. 表格空值处理优化**
Ant Design Vue表格中空值处理的完整解决方案，以复购明细表格为例：

**问题场景**：
- 表格中的空值显示为空白，影响数据可读性
- 需要统一空值的显示格式（如显示为"-"或"0"）
- 不同类型的列需要不同的格式化处理

**关键发现**：
当表格同时使用 `customRender` 和 `#bodyCell` 模板时，`#bodyCell` 模板会**覆盖** `customRender` 函数！

**错误实现**：
```javascript
// ❌ 只在列定义中使用customRender，会被bodyCell覆盖
{
    title: '复购人数',
    dataIndex: 'repurchaseCustomers',
    customRender: ({ text }) => {
        return text || '-';  // 这个不会生效
    }
}
```

**正确解决方案**：
```vue
<!-- ✅ 在bodyCell模板中统一处理所有列的格式化 -->
<template #bodyCell="{ text, record, column }">
    <span :class="{ 'summary-text': record.repurchaseTimes === '合计' || record.repurchaseTimes === '人均' }">
        <!-- 数字类型列：使用统一的格式化函数 -->
        <template v-if="column.dataIndex === 'repurchaseCustomers' || column.dataIndex === 'repurchaseQuantity' || column.dataIndex === 'avgRepurchaseCycleDays'">
            {{ formatNumberWithZero(text) }}
        </template>
        <!-- 金额类型列：去掉货币符号，统一格式 -->
        <template v-else-if="column.dataIndex === 'repurchaseAmount' || column.dataIndex === 'unitPrice'">
            {{ formatNumberWithZero(text) }}
        </template>
        <!-- 其他列：保持原样 -->
        <template v-else>
            {{ text }}
        </template>
    </span>
</template>
```

**格式化函数设计**：
```javascript
// 空值显示为"0"的格式化函数
function formatNumberWithZero(value) {
    // 处理各种空值情况
    if (value === null || value === undefined || value === '' ||
        value === 'null' || value === 'undefined' ||
        (typeof value === 'string' && value.trim() === '')) {
        return '0';  // 统一显示为0
    }

    const num = parseFloat(value);
    if (isNaN(num)) {
        return '0';  // 无法解析时也显示为0
    }

    // 整数显示为整数，小数保留2位
    return num % 1 === 0 ? num.toString() : num.toFixed(2);
}

// 空值显示为"-"的格式化函数
function formatNumber(value) {
    if (value === null || value === undefined || value === '' ||
        value === 'null' || value === 'undefined' ||
        (typeof value === 'string' && value.trim() === '')) {
        return '-';  // 显示为横线
    }

    const num = parseFloat(value);
    if (isNaN(num)) {
        return '-';
    }

    return num % 1 === 0 ? num.toString() : num.toFixed(2);
}
```

**最佳实践总结**：
- **优先级理解**：`#bodyCell` 模板 > `customRender` 函数
- **统一处理**：在bodyCell中统一处理所有列的格式化逻辑
- **类型区分**：不同类型的数据使用不同的格式化策略
- **空值标准化**：建立项目统一的空值显示标准
- **函数复用**：抽取通用的格式化函数，提高代码复用性
- **用户体验**：统一的数据显示格式提升表格的可读性

### API 调用最佳实践

#### 错误处理机制
```javascript
try {
  const res = await api.someMethod();
  if (res.data) {
    // 处理成功响应
  }
} catch (err) {
  console.error("API调用失败:", err);
  smartSentry.captureError(err); // 错误上报
  message.error('操作失败，请重试');
}
```

#### API 方法确认
- 开发前先检查 API 文件确认正确的方法名
- 常见错误：`queryListAll` vs `queryAll`
- 建议：使用 IDE 的自动补全功能避免拼写错误

## 🗄️ 数据库开发经验

### 多数据源查询优化

#### 字符集兼容性问题
**问题**：不同数据库的字符集排序规则不一致导致 JOIN 失败
```sql
-- 错误示例
SELECT * FROM smart_admin_v3.table1 t1
JOIN lirun.table2 t2 ON t1.code = t2.code;
```

**解决方案**：使用 CONVERT 和 COLLATE 函数统一字符集
```sql
SELECT * FROM smart_admin_v3.table1 t1
JOIN lirun.table2 t2 ON CONVERT(t1.code USING utf8mb4) COLLATE utf8mb4_general_ci = 
                        CONVERT(t2.code USING utf8mb4) COLLATE utf8mb4_general_ci;
```

#### 实时计算 vs 预计算权衡

**场景**：客户分类计算
- **预计算方案**：定时同步数据到汇总表
  - 优点：查询速度快
  - 缺点：数据可能不准确，同步复杂

- **实时计算方案**：直接查询源数据
  - 优点：数据准确性高
  - 缺点：查询复杂度高

**最佳实践**：根据业务需求选择
- 对准确性要求高的场景：选择实时计算
- 对性能要求高的场景：选择预计算 + 定时同步

### 复购明细统计逻辑经验

#### 数据重复计算问题
**问题场景**: 复购明细统计中，客户数据被重复计算

**问题示例**:
```sql
-- ❌ 错误逻辑：按客户最大复购次数分组，导致重复计算
SELECT
    rc.复购次数,
    COUNT(CASE WHEN cmr.最大复购次数 >= rc.复购次数 THEN 1 END) as 复购人数,
    SUM(CASE WHEN cmr.最大复购次数 >= rc.复购次数 THEN cmr.客户总复购件数 ELSE 0 END) as 复购件数
FROM RepurchaseCategories rc
LEFT JOIN CustomerMaxRepurchase cmr ON cmr.最大复购次数 >= rc.复购次数
-- 问题：一个有2次复购的客户，其总数据会同时计入第1次和第2次分组
```

**正确解决方案**:
```sql
-- ✅ 正确逻辑：按实际复购记录统计
SELECT
    CASE WHEN frd.复购次数 <= 10 THEN frd.复购次数 ELSE 999 END as 复购次数分组,
    COUNT(DISTINCT frd.客户唯一编码) as 复购人数,
    SUM(frd.日订单数量) as 复购件数,  -- 直接统计该次复购的实际数据
    SUM(frd.日订单金额) as 复购金额
FROM FilteredRepurchaseData frd  -- 每条记录代表一次具体的复购行为
GROUP BY CASE WHEN frd.复购次数 <= 10 THEN frd.复购次数 ELSE 999 END
```

**核心理解**:
- **第N次复购** = 所有客户第N次复购行为的汇总数据
- **避免重复**: 每个复购记录只被计算一次
- **数据准确**: 确保件数、金额等指标的准确性

#### 复购统计的两种思路对比

**思路1: 按客户去重（适用于人数统计）**
```sql
-- 每个客户只出现在其最大复购次数对应的分组中
-- 第1次复购：只有最大复购次数=1的客户
-- 第2次复购：只有最大复购次数=2的客户
-- 特点：合计人数 = 实际复购客户总数
```

**思路2: 按记录统计（适用于明细分析）**
```sql
-- 每个复购记录独立统计
-- 第1次复购：所有客户的第1次复购记录汇总
-- 第2次复购：所有客户的第2次复购记录汇总
-- 特点：数据不重复，业务含义清晰
```

**选择原则**:
- 人数分析：使用思路1，避免客户重复计算
- 明细分析：使用思路2，确保数据准确性
- 业务需求：根据实际业务场景选择合适的统计方式

### SQL 优化经验

#### MyBatis动态SQL条件逻辑
**回购ID精确分析的条件逻辑设计**：
```xml
<!-- 优先级条件逻辑：回购ID > 货品ID -->
<choose>
    <when test="queryForm.repurchasePlatformGoodsIds != null and queryForm.repurchasePlatformGoodsIds.size() > 0">
        <!-- 选择了回购ID时，使用回购ID进行筛选 -->
        AND om.平台货品ID IN
        <foreach collection="queryForm.repurchasePlatformGoodsIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </when>
    <otherwise>
        <!-- 没有选择回购ID时，使用货品ID进行筛选 -->
        <if test="queryForm.platformGoodsIds != null and queryForm.platformGoodsIds.size() > 0">
            AND om.平台货品ID IN
            <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </otherwise>
</choose>
```

**关键要点**：
1. **空数组处理**: 当数组为空时，`foreach`会生成空的`IN ()`子句，导致SQL语法错误
2. **条件判断**: 必须同时检查`!= null`和`size() > 0`
3. **优先级设计**: 使用`choose-when-otherwise`实现互斥条件
4. **向下兼容**: 新功能不影响原有逻辑

#### 复杂EXISTS子查询设计
**回购ID精确分析中的客户验证逻辑**：
```sql
-- 验证客户是否为付款期间的复购客户
AND EXISTS (
    SELECT 1 FROM lirun.订单明细 om_check
    WHERE om_check.客户唯一编码 = om.客户唯一编码
        AND om_check.平台货品ID IN (货品ID列表)
        AND om_check.付款时间 >= #{queryForm.startDate}
        AND om_check.付款时间 < DATE_ADD(#{queryForm.endDate}, INTERVAL 1 DAY)
        AND [基础过滤条件]
    GROUP BY om_check.客户唯一编码
    HAVING COUNT(DISTINCT DATE(om_check.付款时间)) >= 2
)
```

**设计原则**：
- **EXISTS优化**: 比JOIN性能更好，只需验证存在性
- **GROUP BY + HAVING**: 实现复购客户的精确识别
- **参数复用**: 在子查询中复用主查询的参数

#### CTE（公共表表达式）的使用
```sql
-- 使用 CTE 提高 SQL 可读性
WITH FilteredOrders AS (
    SELECT * FROM lirun.订单明细
    WHERE 付款时间 BETWEEN #{startDate} AND #{endDate}
),
CustomerClassification AS (
    SELECT
        客户唯一编码,
        CASE
            WHEN EXISTS (历史订单查询) THEN '老客'
            ELSE '新客'
        END as 客户类型
    FROM FilteredOrders
)
SELECT * FROM CustomerClassification;
```

#### 索引优化策略
- **组合索引**：遵循最左前缀原则
- **覆盖索引**：减少回表查询
- **条件索引**：针对特定条件创建索引

## 🔧 系统架构经验

### 配置管理
**双重配置文件问题**：
- SmartAdmin 项目需要同时维护两个位置的配置文件
- 位置1：`smart-admin-api-java17-springboot3/sa-base/src/main/resources/dev/sa-base.yaml`
- 位置2：`sa-base/src/main/resources/dev/sa-base.yaml`

**解决方案**：
- 建立配置同步检查清单
- 使用脚本自动同步配置文件
- 在部署流程中增加配置一致性检查

### 权限控制设计
**菜单权限配置**：
```sql
-- 标准菜单权限配置
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, path, component, perms) VALUES
(306, '客户管理', 1, 0, NULL, NULL, NULL),
(307, '客户视角', 2, 306, '/business/customer/view', '/business/customer/customer-view', 'customer:view:query');
```

**权限验证**：
- 前端：路由守卫 + 按钮权限控制
- 后端：注解 + AOP 拦截

## 🚨 问题排查经验

### 常见错误类型

#### 1. API 调用错误
**错误信息**：`TypeError: employeeApi.queryListAll is not a function`
**排查步骤**：
1. 检查 API 文件中的实际方法名
2. 确认 import 语句正确
3. 验证 API 方法的参数和返回值

#### 2. 数据库连接错误
**错误信息**：`Access denied for user 'root'@'IP地址'`
**排查步骤**：
1. 检查数据库用户权限
2. 验证 IP 白名单设置
3. 确认密码正确性

#### 3. 字符集问题
**错误信息**：`Illegal mix of collations`
**排查步骤**：
1. 检查表的字符集设置
2. 使用 CONVERT 函数统一字符集
3. 修改表结构统一排序规则

#### 4. 数据统计逻辑错误
**错误现象**：统计数据与预期不符，出现重复计算
**排查步骤**：
1. **理解业务需求**：明确统计的业务含义
2. **分析数据结构**：查看原始数据的组织方式
3. **验证统计逻辑**：用简单案例验证SQL逻辑
4. **对比预期结果**：与手工计算结果对比

**案例分析**：
```sql
-- 问题SQL：客户数据被重复计算
SELECT 复购次数, SUM(客户总数据) FROM 客户汇总表
WHERE 最大复购次数 >= 复购次数 GROUP BY 复购次数;

-- 修复SQL：按实际记录统计
SELECT 复购次数, SUM(单次数据) FROM 复购记录表
GROUP BY 复购次数;
```

#### 5. 前端样式显示问题
**错误现象**：组件边框被截断、布局异常
**排查步骤**：
1. **使用开发者工具**：检查元素的计算样式
2. **分析容器层级**：确认父容器的overflow和尺寸设置
3. **简化样式规则**：移除不必要的复杂样式
4. **测试边界情况**：验证不同内容长度下的显示效果

**解决原则**：
- 优先使用简单的解决方案（如调整margin）
- 避免使用!important强制覆盖样式
- 保持与其他组件样式的一致性

#### 6. 表单验证逻辑问题
**错误现象**：选择"全部货品"后仍提示"请选择货品ID"
**问题根源**：验证逻辑只检查数组是否为空，未考虑特殊标识

**错误逻辑**：
```javascript
// ❌ 只检查数组长度
if (!queryForm.selectedProductIds || queryForm.selectedProductIds.length === 0) {
    return { isValid: false, message: '请先选择货品ID' };
}
```

**正确逻辑**：
```javascript
// ✅ 同时检查具体选择和特殊标识
const hasSelectedProducts = queryForm.selectedProductIds && queryForm.selectedProductIds.length > 0;
const hasSelectedAllProducts = selectedProducts.value.length === 1 && selectedProducts.value[0].goodsId === 'ALL_PRODUCTS';

if (!hasSelectedProducts && !hasSelectedAllProducts) {
    return { isValid: false, message: '请先选择货品ID' };
}
```

**经验总结**：
- 特殊标识需要在验证逻辑中特殊处理
- 考虑所有可能的有效状态，不仅仅是常规状态
- 验证逻辑要与业务逻辑保持一致

#### 7. 组件状态管理问题
**错误现象**：重置按钮不会清除某些选择状态
**问题分析**：状态管理不完整，遗漏了新增的状态变量

**解决方案**：
```javascript
// 重置时需要清除所有相关状态
function resetQuery() {
    // 清除基础查询条件
    Object.assign(queryForm, queryFormState);

    // 清除货品选择状态
    selectedProducts.value = [{ goodsId: 'ALL_PRODUCTS', shopName: '全部货品', displayName: '全部货品' }];
    queryForm.selectedProductIds = [];

    // 清除回购ID选择状态（新增功能需要额外处理）
    selectedRepurchaseProducts.value = [];
    queryForm.selectedRepurchaseProductIds = [];

    // 清除其他状态
    excludeFlags.value = [];
    analysisType.value = 'repurchaseCustomers';
}
```

**经验总结**：
- 新增功能时要同步更新重置逻辑
- 建立状态管理检查清单
- 测试重置功能的完整性

#### 8. 鼠标事件冒泡导致的交互问题
**错误现象**：悬停显示的清除按钮无法点击，鼠标移动到按钮上时图标又变回了默认状态
**问题分析**：鼠标事件冒泡问题，当鼠标从输入框移动到清除按钮时，触发了输入框的mouseleave事件

**问题场景**：
```vue
<!-- ❌ 错误实现：只在输入框监听鼠标事件 -->
<a-input
    @mouseenter="inputHovered = true"
    @mouseleave="inputHovered = false"
>
    <template #suffix>
        <CloseOutlined v-if="hasSelection && inputHovered" @click="clearSelection" />
        <SelectOutlined v-else />
    </template>
</a-input>
```

**问题流程**：
1. 鼠标悬停在输入框 → `inputHovered = true` → 显示清除按钮
2. 鼠标移动到清除按钮 → 离开输入框区域 → 触发`mouseleave` → `inputHovered = false`
3. 清除按钮消失，变回选择图标 → 无法点击清除

**正确解决方案**：
```vue
<!-- ✅ 正确实现：在输入框和图标容器上都监听鼠标事件 -->
<a-input
    @mouseenter="inputHovered = true"
    @mouseleave="inputHovered = false"
>
    <template #suffix>
        <span
            @mouseenter="inputHovered = true"
            @mouseleave="inputHovered = false"
            style="display: inline-flex; align-items: center;"
        >
            <CloseOutlined v-if="hasSelection && inputHovered" @click="clearSelection" />
            <SelectOutlined v-else />
        </span>
    </template>
</a-input>
```

**核心解决思路**：
- **双重事件监听**：在输入框和图标容器上都监听mouseenter/mouseleave
- **扩大悬停区域**：确保鼠标在整个交互区域内都保持悬停状态
- **事件同步**：两个区域的事件处理逻辑保持一致

**类似问题的通用解决方案**：
```vue
<!-- 通用模式：悬停交互的容器设计 -->
<div class="hover-container" @mouseenter="showAction = true" @mouseleave="showAction = false">
    <div class="main-content">主要内容</div>
    <div class="action-area" @mouseenter="showAction = true" @mouseleave="showAction = false">
        <button v-if="showAction" @click="handleAction">操作按钮</button>
        <icon v-else>默认图标</icon>
    </div>
</div>
```

**经验总结**：
- **识别冒泡问题**：当悬停交互失效时，首先检查是否为鼠标事件冒泡问题
- **扩大监听范围**：在所有相关的交互元素上都添加鼠标事件监听
- **保持状态一致**：确保所有监听器的状态变更逻辑一致
- **测试边界情况**：重点测试鼠标在不同元素间移动的交互效果
- **参考成熟组件**：学习Ant Design等成熟组件库的交互设计模式

### 调试技巧

#### 前端调试
- 使用 Vue DevTools 查看组件状态
- Console.log 输出关键变量
- Network 面板检查 API 请求

#### 后端调试
- 使用 P6Spy 监控 SQL 执行
- 日志级别调整为 DEBUG
- 使用断点调试复杂逻辑

#### 数据库调试
- 使用 EXPLAIN 分析查询计划
- 开启慢查询日志
- 使用 SHOW PROCESSLIST 查看运行状态

## 💡 开发最佳实践

### 功能扩展最佳实践

#### 基于回购ID功能开发的经验总结

**1. 需求分析阶段**
- **明确业务逻辑**：理解精确复购分析的业务含义
- **设计数据流**：从前端选择到后端查询的完整数据流
- **考虑边界情况**：空选择、相同ID、不同ID等场景

**2. 技术设计阶段**
- **组件复用策略**：评估现有组件的复用可能性
- **数据结构设计**：新增字段的命名和类型设计
- **向下兼容原则**：确保新功能不影响现有功能

**3. 实现阶段**
- **前端实现顺序**：UI组件 → 状态管理 → API调用
- **后端实现顺序**：参数接收 → SQL逻辑 → 测试验证
- **渐进式开发**：先实现基础功能，再优化用户体验

**4. 测试验证阶段**
- **功能测试**：验证各种选择组合的正确性
- **边界测试**：测试空选择、极值等边界情况
- **兼容性测试**：确保原有功能不受影响

**5. 优化改进阶段**
- **性能优化**：添加缓存机制，提升查询性能
- **用户体验优化**：添加清除按钮，优化交互流程
- **代码质量优化**：重构重复代码，添加注释文档

#### 组件扩展设计模式

**模式1: 复制扩展模式**
```javascript
// 适用场景：功能相似但独立的组件
const productSelectModalRef = ref();           // 原有组件
const repurchaseProductSelectModalRef = ref(); // 复制的组件

// 优点：独立性强，互不影响
// 缺点：代码重复，维护成本高
```

**模式2: 参数化扩展模式**
```javascript
// 适用场景：功能高度相似的组件
<ProductSelectModal
    ref="productSelectModalRef"
    :type="'product'"
    @confirm="onProductSelected"
/>
<ProductSelectModal
    ref="repurchaseProductSelectModalRef"
    :type="'repurchase'"
    @confirm="onRepurchaseProductSelected"
/>

// 优点：代码复用，维护成本低
// 缺点：组件复杂度增加
```

**选择原则**：
- 功能差异大：使用复制扩展模式
- 功能相似度高：使用参数化扩展模式
- 考虑未来扩展：评估后续可能的变化

#### SQL条件逻辑设计模式

**模式1: 优先级条件模式**
```xml
<!-- 适用场景：多个条件有优先级关系 -->
<choose>
    <when test="高优先级条件">高优先级逻辑</when>
    <when test="中优先级条件">中优先级逻辑</when>
    <otherwise>默认逻辑</otherwise>
</choose>
```

**模式2: 组合条件模式**
```xml
<!-- 适用场景：多个条件需要组合使用 -->
<if test="条件A">AND 逻辑A</if>
<if test="条件B">AND 逻辑B</if>
<if test="条件C">AND 逻辑C</if>
```

**模式3: 互斥条件模式**
```xml
<!-- 适用场景：条件互斥，只能选择一个 -->
<choose>
    <when test="条件A">逻辑A</when>
    <when test="条件B">逻辑B</when>
    <otherwise>默认逻辑</otherwise>
</choose>
```

### 代码规范
1. **命名规范**：使用有意义的变量和方法名
2. **注释规范**：关键逻辑必须有注释说明
3. **错误处理**：所有 API 调用都要有错误处理
4. **代码复用**：提取公共方法和组件

### 测试策略
1. **单元测试**：核心业务逻辑编写单元测试
2. **集成测试**：API 接口编写集成测试
3. **端到端测试**：关键业务流程编写 E2E 测试

### 性能优化
1. **前端优化**：
   - 组件懒加载
   - 图片懒加载
   - 合理使用缓存

2. **后端优化**：
   - 数据库连接池配置
   - 缓存策略设计
   - 异步处理长耗时任务

3. **数据库优化**：
   - 索引设计优化
   - 查询语句优化
   - 分库分表策略

## 📚 学习资源

### 官方文档
- [Vue3 官方文档](https://v3.vuejs.org/)
- [Ant Design Vue](https://antdv.com/)
- [Spring Boot 官方文档](https://spring.io/projects/spring-boot)

### 社区资源
- [SmartAdmin 官方社区](https://smartadmin.vip)
- [GitHub Issues](https://github.com/1024-lab/smart-admin)

---

💡 **提示**：开发经验需要在实践中不断积累和总结，建议团队定期进行技术分享和经验交流，共同提升开发效率和代码质量。
