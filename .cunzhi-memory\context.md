# 项目上下文信息

- SmartAdmin项目结构：
1. 项目类型：前后端分离的企业级管理系统
2. 前端：smart-admin-web-javascript (Vue3 + Ant Design Vue + Vite)
3. 后端：smart-admin-api-java17-springboot3 (Java17 + SpringBoot3 + Sa-Token + Mybatis-Plus)
4. 移动端：smart-app (UniApp + Vue3)
5. 数据库：sql目录包含数据库脚本
6. 技术栈：满足三级等保要求，支持加解密、数据脱敏等安全功能
- 客户管理模块详细结构：
1. 前端页面：customer-view.vue（主页面）、customer-link.vue（客户关联）
2. 前端组件：customer-view-detail-modal.vue、customer-view-statistics-modal.vue、customer-record-modal.vue等
3. 前端API：customer-view-api.js、customer-order-api.js、customer-link-api.js、customer-record-api.js
4. 后端Controller：CustomerViewController、CustomerOrderController、CustomerLinkController、CustomerRecordController
5. 后端Service：CustomerViewService、CustomerOrderService、CustomerLinkService、CustomerRecordService、LirunJdbcService
6. 数据实体：CustomerEntity、CustomerLinkEntity、CustomerRecordEntity、PlatformProductEntity
7. 权限标识：customer:view:query、customer:view:detail、customer:view:export、customer:record:manage
- 链接视角功能详细结构：前端页面customer-link.vue，后端Controller/Service/DAO，数据库表lirun.订单明细和smart_admin_v3.t_menu，菜单ID 3074，权限包括查询、统计分析、下载明细等
- SQL文件夹已完成重新整理：1)创建了4个分类目录：01-database-init(数据库初始化)、02-version-updates(版本更新)、03-feature-scripts(功能脚本)、04-deprecated(已废弃)；2)将customer_classification_enhancement.sql移至废弃目录，因为已改为实时计算方案；3)整理了版本更新脚本、功能脚本和代码生成模板；4)创建了详细的README文档说明各脚本用途和使用方法
- 用户希望为员工管理系统添加高级安全验证功能，当执行敏感操作（禁用/启用员工、重置密码）时需要向超级管理员邮箱发送验证。需要分析技术可行性、架构影响和实现方案。
- 链接视角页面复购明细表格新增客单价字段：客单价=复购子订单付款金额÷付款人数，位置在复购金额右侧，保留2位小数，货币格式，支持排序
- 链接视角页面复购明细列表功能修改：在前端Vue组件中过滤掉第0次购买记录，只显示第1次及以后的复购记录。修改位置：customer-link.vue第997-1000行，在数据排序前增加过滤逻辑filteredData = rawData.filter(item => item.repurchaseTimes !== '第0次')，确保复购明细表格不显示非复购客户数据
- 链接视角页面复购明细标题增强：在复购明细标题旁边新增统计信息显示，包含"目标客户人数 xxx 人，其中复购人数 xx 人，复购率为 x.xx%"。修改位置：customer-link.vue第242-257行HTML结构调整，第955-966行新增formatRepurchaseRate()方法，第1516-1541行新增CSS样式。复购率计算逻辑：复购人数/付款人数*100%，保留2位小数
- 链接视角页面复购明细统计信息样式优化：数字部分使用橙色高亮显示(#ff7a00)，字体加粗(font-weight: 600)，字号14px。整体文字调整为13px，行高1.5，增加2px上边距。HTML结构中为数字部分添加number-highlight类名
- 链接视角页面目标客户人数修正：新增targetCustomerCount字段，基于付款日期范围计算，不受回购日期影响。修改位置：1)CustomerLinkAnalysisVO.java第24-25行新增字段；2)CustomerLinkMapper.xml第209行新增子查询；3)customer-link.vue第246行使用targetCustomerCount，第956-967行修改复购率计算逻辑使用targetCustomerCount作为分母
- 链接视角页面复购率分析区域付款人数修正：将复购率分析区域的付款人数显示也修改为使用targetCustomerCount，确保不受回购日期影响。修改位置：customer-link.vue第224行，统一使用targetCustomerCount || paymentUserCount的兼容逻辑
- 链接视角页面复购人数逻辑修正：新增targetRepurchaseUserCount字段，基于付款日期范围计算复购人数，不受回购日期影响。修改位置：1)CustomerLinkAnalysisVO.java第30-31行新增字段；2)CustomerLinkMapper.xml第211行新增子查询；3)customer-link.vue第214行复购率分析区域、第247行复购明细标题区域、第965行复购率计算方法，统一使用targetRepurchaseUserCount优先逻辑
- 链接视角页面新增回购ID选项功能完成：1)在customer-link.vue第55-67行新增回购ID输入框，位于回购日期右侧；2)在queryFormState中新增selectedRepurchaseProductIds字段；3)新增回购商品选择相关变量和方法：selectedRepurchaseProducts、showRepurchaseProductSelectModal、onRepurchaseProductSelected、selectedRepurchaseProductText；4)添加独立的回购商品选择弹窗repurchaseProductSelectModalRef；5)在API调用参数中新增repurchasePlatformGoodsIds字段；6)后端CustomerLinkQueryForm.java新增repurchasePlatformGoodsIds字段。回购ID与货品ID功能逻辑完全相同，支持多选，在查询中为AND关系
- 链接视角页面货品ID选择优化：1)在ProductSelectModal组件中新增"选择全部货品"选项，选中时禁用具体商品选择；2)修改customer-link.vue中的商品选择逻辑，支持"全部货品"特殊标识(ALL_PRODUCTS)；3)当选择全部货品时，selectedProductIds传空数组，后端不添加货品ID筛选条件；4)页面初始化和重置时默认选择"全部货品"，提供更好的用户体验
- 链接视角页面表单验证逻辑修复：修改validateQueryForm函数中的货品ID验证逻辑，当选择"全部货品"时(selectedProducts包含ALL_PRODUCTS标识)也应该通过验证，解决了选择全部货品后仍提示"请选择货品ID"的问题
- 链接视角页面SQL语法错误修复：在CustomerLinkMapper.xml的queryAnalysis方法中，修复了当选择"全部货品"时platformGoodsIds为空数组导致的SQL语法错误。在平均复购周期计算的两个子查询中添加了条件判断，当platformGoodsIds为空时不添加IN子句，避免生成空的IN()导致SQL语法错误
- 链接视角页面回购ID筛选逻辑实现：在CustomerLinkMapper.xml中修改了三个关键位置的货品ID筛选逻辑，当选择回购ID时优先使用repurchasePlatformGoodsIds进行筛选：1)RepurchaseCustomers CTE中的EXISTS子查询；2)RepurchaseCycleData CTE中的货品ID筛选；3)queryDetail方法中RepurchasePeriodCustomers CTE的筛选。实现了回购ID精确分析功能
- 链接视角页面复购明细回购ID筛选逻辑修复：修改CustomerLinkMapper.xml中queryDetail方法的ValidOrders CTE，当选择回购ID时，只显示付款期间复购客户在回购期间购买回购ID的明细。通过EXISTS子查询验证客户是否为付款期间复购客户，并使用回购日期范围和回购ID进行筛选，实现了复购明细的回购ID精确分析
- 链接视角页面分析数据回购ID筛选逻辑修复：修复CustomerLinkMapper.xml中queryAnalysis方法的同时间段分析部分，在RepurchaseCustomers CTE的same_period_repurchase子查询中添加回购ID筛选逻辑，当选择回购ID时优先使用repurchasePlatformGoodsIds进行筛选，解决了分析数据复购率计算错误的问题
- 链接视角页面UI优化完成：1)货品ID和回购ID输入框添加清除按钮，可单独清除选择；2)重置按钮不会重置回购ID选择；3)ProductSelectModal添加24小时缓存机制，无搜索条件时使用缓存提升性能；4)点击选择货品ID时不默认选中全部货品，提供更好的用户体验
- 链接视角页面重置逻辑修正：重置按钮会清除回购ID选择，恢复到初始状态。重置后货品ID为"全部货品"，回购ID为空选择，符合用户期望的重置行为
- ProductSelectModal确认逻辑优化：当用户打开货品选择页面但没有选择任何具体货品就点确认时，自动选择"全部货品"，保持默认行为的一致性
- 文档更新完成：1)链接视角模块.md更新到v3.0版本，新增回购ID精确分析功能的完整技术文档，包括业务逻辑、技术实现、UI优化、数据验证等；2)开发经验.md新增回购ID功能开发经验，包括组件复用、SQL条件逻辑、UI优化、问题排查等最佳实践，为后续类似功能开发提供参考
- 货品ID和回购ID清除功能UI优化：参考回购日期的交互方式，改为鼠标悬停时才显示清除按钮，提升用户体验。实现方式：添加mouseenter/mouseleave事件监听，使用响应式变量控制清除按钮的显示/隐藏，悬停时显示CloseOutlined，非悬停时显示SelectOutlined
- 修复清除按钮图标重叠问题：将清除按钮和选择图标改为互斥显示，使用v-if和v-else确保同一位置只显示一个图标，避免重叠导致无法点击的问题。调整图标大小为14px，统一样式
- 修复货品ID清除按钮显示逻辑：添加hasSpecificProductSelected计算属性，只有在选择了具体货品ID（非"全部货品"）时才在悬停时显示清除按钮。避免在默认"全部货品"状态下显示不必要的清除按钮
- 修复清除按钮鼠标事件冒泡问题：在suffix模板中添加span容器，同时监听输入框和图标容器的mouseenter/mouseleave事件，确保鼠标移动到清除按钮上时不会触发mouseleave导致图标切换，解决无法点击清除按钮的问题
- 开发经验文档更新：新增鼠标事件冒泡导致的交互问题解决经验，详细记录了悬停显示清除按钮无法点击的问题分析、解决方案和通用模式，包括双重事件监听、扩大悬停区域等核心解决思路，为后续类似问题提供参考
- 修复复购明细表格空值显示问题：为复购人数、复购件数、复购金额、平均复购周期天数列添加customRender函数，使用formatNumber方法将空值显示为"-"，提升表格数据的可读性
- 修复复购明细表格空值显示的根本问题：发现bodyCell模板会覆盖列定义中的customRender函数，更新bodyCell模板来正确处理所有列的空值格式化，包括复购人数、复购件数、复购金额、平均复购周期天数等，确保空值正确显示为"-"
- 统一复购明细表格数据显示格式：1)去掉复购金额和客单价前面的¥符号；2)所有空值统一显示为0而不是"-"；3)新增formatNumberWithZero函数处理空值为0的格式化需求；4)更新bodyCell模板和列定义的customRender函数使用新的格式化方法
- 文档更新完成：1)在开发经验.md中新增"表格空值处理优化"章节，详细记录了Ant Design Vue表格中bodyCell模板覆盖customRender函数的问题及解决方案；2)在链接视角模块.md的v3.0版本中添加表格空值处理优化的记录，为后续开发提供参考
