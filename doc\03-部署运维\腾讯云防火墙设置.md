好的，非常乐意为您整理。

这是一个从零开始，在您的Ubuntu服务器上配置SSH，使其仅允许中国大陆IP访问的完整操作文档。本文档总结了我们之前所有的讨论、排查和最终的解决方案，您可以保存下来以备后用。

-----

### **文档：通过 IPSet 和 IPTables/UFW 实现SSH访问的地理位置限制（仅限中国IP）**

#### **目标**

为了大幅提升服务器的安全性，我们将配置防火墙，使其仅允许来自中国大陆地区的IP地址访问SSH服务端口，并自动拦截所有其他海外IP的连接请求。同时，我们将设置一个自动更新机制，以确保IP地址库的长期有效性。

#### **核心工具**

  * **`ipset`**: 用于高效地管理和查询大量的IP地址集合。
  * **`iptables` / `UFW`**: Linux核心防火墙工具，用于执行具体的拦截或放行规则。
  * **`cron`**: Linux标准的定时任务工具，用于实现自动化。

-----

### **第一部分：手动配置与部署**

#### **步骤1：安装依赖工具**

首先，确保我们的工具都已安装。`iptables-persistent`用于让防火墙规则在服务器重启后依然生效。

```bash
sudo apt-get update
sudo apt-get install ipset iptables-persistent -y
```

#### **步骤2：准备IP地址列表**

我们需要一个可靠的、包含所有中国IP段的列表文件。

1.  **创建专用目录** (推荐，用于存放相关文件)
    ```bash
    sudo mkdir -p /opt/my_firewall
    ```
2.  **下载IP列表文件**
    ```bash
    wget -O /opt/my_firewall/china_ip_list.txt https://raw.githubusercontent.com/17mon/china_ip_list/master/china_ip_list.txt
    ```

#### **步骤3：创建并填充 IPSet 集合**

现在，我们将下载的IP列表加载到 `ipset` 中，创建一个名为 `cn_ssh_ips` 的IP地址“白名单”。

1.  **创建新的IP集合**
    ```bash
    sudo ipset create cn_ssh_ips hash:net
    ```
2.  **将IP列表导入集合** (此过程可能需要一到两分钟，请耐心等待)
    ```bash
    for ip in $(cat /opt/my_firewall/china_ip_list.txt); do sudo ipset add cn_ssh_ips $ip; done
    ```

#### **步骤4：配置防火墙规则（关键步骤）**

由于您的系统同时使用了`UFW`，我们需要将规则添加到正确的位置以确保最高优先级。**请将下面所有命令中的 `2938` 替换成您真实的SSH端口号。**

1.  **（预检查）清理可能存在的冲突规则**
    运行 `sudo ufw status numbered` 检查是否存在类似 `2938/tcp ALLOW IN Anywhere` 的规则。如果存在，请用 `sudo ufw delete [序号]` 将其删除。

2.  **插入精确的IP限制规则**
    我们将规则插入到 `ufw-user-input` 链的顶端，使其最先被执行。

    ```bash
    # 规则1：允许来自中国IP集合(cn_ssh_ips)的IP访问2938端口
    sudo iptables -I ufw-user-input 1 -p tcp --dport 2938 -m set --match-set cn_ssh_ips src -j ACCEPT

    # 规则2：拒绝所有其他访问2938端口的请求
    sudo iptables -I ufw-user-input 2 -p tcp --dport 2938 -j DROP
    ```

#### **步骤5：持久化保存规则**

让 `ipset` 和 `iptables` 的规则在服务器重启后能自动加载。

1.  **保存 IPSet 集合**
    ```bash
    sudo ipset save cn_ssh_ips -f /etc/ipset.conf
    ```
2.  **保存 IPTables 防火墙规则**
    ```bash
    sudo netfilter-persistent save
    ```

此时，您的手动配置已完成，防火墙已生效。您可以通过海外IP（VPN或在线工具）测试SSH端口来验证是否被成功拦截。

-----

### **第二部分：设置自动化更新（一劳永逸）**

IP地址库不是一成不变的，我们需要让服务器自动更新它。

#### **步骤1：创建自动更新脚本**

1.  **创建脚本文件**
    ```bash
    sudo nano /opt/my_firewall/update_ipset.sh
    ```
2.  **将以下脚本内容完整地复制粘贴进去**
    ```bash
    #!/bin/bash

    # --- 变量定义 ---
    IPSET_NAME="cn_ssh_ips"
    IP_LIST_URL="https://raw.githubusercontent.com/17mon/china_ip_list/master/china_ip_list.txt"
    IPSET_SAVE_PATH="/etc/ipset.conf"

    # --- 脚本主体 ---
    TEMP_IPSET_NAME="${IPSET_NAME}_temp"
    TEMP_IP_LIST_FILE="/tmp/${IPSET_NAME}_list.tmp"

    # 下载最新的IP列表
    wget -qO "$TEMP_IP_LIST_FILE" "$IP_LIST_URL"

    # 检查下载是否成功
    if [ $? -eq 0 ] && [ -s "$TEMP_IP_LIST_FILE" ]; then
        # 创建临时ipset
        sudo ipset create "$TEMP_IPSET_NAME" hash:net >/dev/null 2>&1 || sudo ipset flush "$TEMP_IPSET_NAME"
        
        # 导入新IP到临时ipset
        for ip in $(cat "$TEMP_IP_LIST_FILE"); do
            sudo ipset add "$TEMP_IPSET_NAME" "$ip" >/dev/null 2>&1
        done
        
        # 原子交换，瞬间完成，服务不中断
        sudo ipset swap "$IPSET_NAME" "$TEMP_IPSET_NAME"
        
        # 销毁临时ipset
        sudo ipset destroy "$TEMP_IPSET_NAME"
        
        # 持久化保存更新后的ipset
        sudo ipset save "$IPSET_NAME" -f "$IPSET_SAVE_PATH"
        
        echo "$(date): IP set '$IPSET_NAME' updated successfully."
    else
        echo "$(date): Failed to download IP list."
        exit 1
    fi

    # 清理临时文件
    rm -f "$TEMP_IP_LIST_FILE"
    exit 0
    ```
3.  **保存并退出** (按 `Ctrl + X` -\> `Y` -\> `Enter`)。
4.  **赋予脚本执行权限**
    ```bash
    sudo chmod +x /opt/my_firewall/update_ipset.sh
    ```

#### **步骤2：设置定时任务（Cron Job）**

我们让服务器每月自动运行一次这个更新脚本。

1.  **打开定时任务编辑器**

    ```bash
    crontab -e
    ```

2.  **在文件末尾添加以下一行**

    ```
    0 3 1 * * /bin/bash /opt/my_firewall/update_ipset.sh >> /var/log/ipset_update.log 2>&1
    ```

    *这行命令的含义是：在每个月的1号的凌晨3点0分，执行我们的更新脚本，并将执行日志记录到 `/var/log/ipset_update.log` 文件中。*

3.  **保存并退出编辑器。**

-----

**文档结束**

至此，您已经拥有一个安全、健壮且能自动维护的SSH地理位置防火墙。