# 开发规范和规则

- SmartAdmin技术架构规范：
1. 权限系统：基于Sa-Token，使用api_perms字段进行权限验证，三级权限结构（一级菜单、二级菜单、功能点）
2. 前端架构：Vue3 + Ant Design Vue + Vite + Pinia状态管理，支持多环境配置
3. 后端架构：SpringBoot3 + Mybatis-Plus + Redis缓存，四层架构（controller, service, manager, dao）
4. 安全特性：满足三级等保要求，支持接口加解密、数据脱敏、登录限制等
5. 数据库：MySQL，支持多环境配置，使用Druid连接池
- 客户视角页面修改记录：
1. 将默认查询条件从"首次成交日期"改为"最后成交日期"
2. 修改了表单label显示文本
3. 修改了数据绑定字段从firstPurchaseDateRange改为lastPurchaseDateRange
4. 修改了查询参数字段从firstPurchaseDateBegin/End改为lastPurchaseDateBegin/End
5. 更新了初始化函数和重置函数的注释
6. 保持了高级搜索中两个日期字段的完整功能
7. 确保了导出功能使用新的查询条件
- 客户视角页面空值筛选功能修改记录：
1. 前端修改：为"流失风险"和"会员等级"下拉选择器添加"空值"选项，value为"NULL"
2. 后端修改：在LirunJdbcService中增加空值查询逻辑，当接收到"NULL"值时查询IS NULL OR = ''的记录
3. 显示逻辑：更新getChurnRiskColor、getMemberLevelColor、getMemberLevelText函数处理空值显示
4. 表格显示：流失风险空值显示为"未设置"，会员等级空值通过getMemberLevelText处理
5. 重置功能：现有重置逻辑已正确处理空值选项的清除
6. 导出功能：同步更新导出查询中的空值处理逻辑
- 客户视角页面空值显示优化修改记录：
1. 下拉选择器显示：将"流失风险"和"会员等级"的空值选项显示文本从"空值"改为"-"
2. 选项位置：将"-"选项移至下拉列表第一位，作为默认显示位置
3. 表格显示：流失风险空值从"未设置"改为"-"，会员等级空值通过getMemberLevelText函数返回"-"
4. 保持功能：value值仍为"NULL"，后端查询逻辑不变
5. UI统一性：所有空值在界面上统一显示为"-"符号，简洁明了
- 客户视角页面空值选项默认选中修改记录：
1. 修改queryFormState初始状态：将churnRisk和memberLevel的默认值从undefined改为'NULL'
2. 修改resetAdvancedSearch函数：重置时将churnRisk和memberLevel恢复为'NULL'而不是undefined
3. 用户体验：用户打开高级搜索时，流失风险和会员等级默认选中"-"选项
4. 功能保持：后端查询逻辑不变，重置功能正确恢复默认状态
5. 界面效果：下拉选择器默认显示"-"选项，用户可手动清除或更改
- 链接视角页面UI调整：查询按钮功能替换为统计分析，统计分析按钮隐藏（display: none），保持查询按钮外观不变
- 员工管理重置密码功能需要添加当前密码验证，提升安全性，防止未授权的密码重置操作
- 员工管理系统存在严重安全漏洞：超级管理员账号可以被普通管理员编辑、禁用，需要实施完整保护方案，包括后端Service层加固、前端UI层隐藏、数据库层约束
- 复购率计算功能需求：基于付款日期范围筛选基础数据，识别复购客户，然后验证这些复购客户在回购日期期间内是否有复购行为，最终计算复购率 = (在回购日期期间内有复购的客户数 / 付款日期范围内的总客户数) × 100%
- 链接视角页面复购率计算逻辑修复：跨时间段分析中，复购客户定义应为"在付款期间购买过 + 在回购期间也购买过的客户"，当前代码只验证了回购期间购买，导致缩短回购日期范围时复购人数异常增加的bug
- 链接视角页面跨时间段复购分析正确逻辑：先基于付款日期范围找出复购客户（购买天数≥2），然后在这些复购客户中筛选出在回购日期范围内也有购买行为的客户。这样确保回购期间的复购人数不会超过付款期间的复购人数，符合业务逻辑
- 链接视角页面跨时间段复购分析最终修复：两步验证逻辑，第一步找出付款期间的复购客户（购买天数≥2），第二步在这些客户中筛选出在回购期间也是复购客户（购买天数≥2）的人数。关键是第二步必须验证"也是复购客户"而不仅仅是"有购买记录"
