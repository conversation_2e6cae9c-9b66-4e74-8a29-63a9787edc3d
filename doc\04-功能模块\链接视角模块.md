# 链接视角页面数据库结构文档

## 📋 概述

本文档记录了链接视角页面的数据库结构、查询逻辑和实现方案。链接视角页面主要用于分析客户的复购行为和链接效果。

**最后更新时间**: 2025-08-05
**版本**: v3.0 (新增回购ID精确分析功能和UI优化)

## 🏗️ 架构变更历史

### v1.0 (已废弃)
- 使用 `smart_admin_v3.crm_客户查询` 表存储预计算的客户数据
- 通过定时任务同步数据，存在数据滞后问题
- 客户分类逻辑依赖同步表中的 `客户类型` 字段

### v2.0 (当前版本)
- 直接从 `lirun` 数据库实时计算客户数据
- 移除对同步表的依赖，解决数据滞后问题
- 实现真正的实时数据分析

### v2.1 (历史版本)
- 新增回购日期验证功能
- 支持验证复购客户在指定回购期间的实际复购行为
- 提供验证后的复购率指标，提高数据准确性

### v2.2 (最新版本)
- **修复跨时间段复购分析逻辑**：区分跨时间段分析和同时间段分析的不同复购定义
- **修复复购明细统计错误**：解决复购客户重复计算和数据不一致问题
- **修复复购率计算精度**：将四舍五入改为截断，确保前后端计算一致性
- **优化SQL性能**：解决HAVING子句中CROSS JOIN的语法错误

### v2.3 (历史版本)
- **修复跨时间段复购客户识别逻辑错误**：修正了当付款日期≠回购日期时，复购人数计算错误的问题
- **问题描述**：原逻辑只验证客户在回购期间有购买，未验证在付款期间也有购买，导致缩短回购日期范围时复购人数异常增加
- **第一次修复**：改为先从付款期间的目标客户中筛选，再验证这些客户在回购期间是否也有购买行为
- **第二次修复**：进一步修正逻辑，确保验证的是客户在回购期间"也是复购客户"，而不仅仅是"有购买记录"
- **最终逻辑**：先找付款期间的复购客户，再在这些客户中筛选出在回购期间也是复购客户的人数
- **影响范围**：queryAnalysis和queryDetail两个核心查询方法，downloadOrderDetail方法逻辑本身正确无需修改

### v2.4 (历史版本)
- **重新修正跨时间段复购分析的业务逻辑**：基于实际业务需求，修正了v2.3中过于严格的复购定义
- **核心逻辑调整**：跨时间段分析时，只需验证付款期间的复购客户在回购期间是否有购买记录，无需再验证是否复购
- **业务合理性**：已在付款期间证明复购能力的客户，在回购期间的任何购买都应视为有效的回购行为
- **复购明细显示修复**：修正跨时间段分析时复购明细的显示逻辑，基于回购期间的购买天数计算复购次数
- **显示逻辑**：回购期间购买1天显示"第1次"，购买2天显示"第2次"，避免出现"第0次"的奇怪显示
- **测试验证**：使用货品761502031104验证，付款期间52个复购客户，跨时间段分析结果41人，符合预期

### v2.5 (历史版本)
- **修复复购明细统计逻辑错误**：解决了按客户去重导致的数据重复计算问题
- **问题描述**：一个客户有2次复购记录，但在统计时被重复计算，导致合计数据不准确
- **修复方案**：改为按实际复购记录统计，第N次复购只统计第N次复购的实际数据
- **统计逻辑**：第1次复购显示所有客户第1次复购的件数和金额，第2次复购显示所有客户第2次复购的件数和金额
- **数据准确性**：确保每个复购记录只被计算一次，避免重复统计
- **前端样式修复**：解决货品ID输入框右边框显示不全的问题，通过调整容器右边距解决

### v3.0 (当前版本)
- **新增回购ID精确分析功能**：支持选择特定回购商品ID进行精确复购分析
- **核心功能**：当同时选择货品ID和回购ID时，分析付款期间复购客户在回购期间购买指定回购ID的行为
- **业务价值**：提供更精准的复购分析，支持特定商品的复购效果评估
- **前端优化**：货品ID支持"全部货品"选择，添加单独清除功能，优化用户体验
- **缓存机制**：商品选择弹窗增加24小时缓存，提升查询性能
- **UI改进**：优化输入框交互，支持独立清除选择，重置逻辑优化
- **表格空值处理优化**：修复复购明细表格空值显示问题，统一空值显示为0，去掉金额列的¥符号
- **表格渲染机制修复**：解决bodyCell模板覆盖customRender函数的问题，统一在bodyCell中处理格式化逻辑

## 🗄️ 数据库结构

### 主要数据源

#### lirun.订单明细 (核心数据表)
```sql
-- 关键字段说明
平台货品ID        VARCHAR    -- 商品唯一标识
客户唯一编码      VARCHAR    -- 客户唯一标识  
付款时间         DATETIME   -- 订单付款时间
付款金额         DECIMAL    -- 实际付款金额
已付            DECIMAL    -- 已付金额
订单状态         VARCHAR    -- 订单状态
订单来源         VARCHAR    -- 订单来源
标记名称         VARCHAR    -- 订单标记
```

### 数据过滤条件
```sql
-- 有效订单过滤条件
WHERE om.订单状态 != '线下退款'
  AND NOT (om.订单来源 = '手工创建' AND om.订单状态 = '已取消')
  AND om.标记名称 != '贴贴贴贴贴'
```

## 🔍 核心查询逻辑

### 1. 客户分类逻辑 (新客/老客)

```sql
-- 实时客户分类计算
CASE 
    WHEN EXISTS (
        SELECT 1 FROM lirun.订单明细 hist 
        WHERE hist.客户唯一编码 = fo.客户唯一编码 
            AND hist.付款时间 < #{queryForm.startDate}
            AND hist.订单状态 != '线下退款'
            AND NOT (hist.订单来源 = '手工创建' AND hist.订单状态 = '已取消')
            AND hist.标记名称 != '贴贴贴贴贴'
    ) THEN '老客'
    ELSE '新客'
END as 客户类型
```

**分类规则**:
- **新客**: 在查询开始日期之前没有购买记录的客户
- **老客**: 在查询开始日期之前有购买记录的客户

### 2. 复购分析逻辑 (v2.2 重大更新)

#### 2.1 智能场景识别
系统自动判断分析类型：
```sql
-- 分析类型判断
CrossPeriodAnalysis AS (
    SELECT
        CASE
            WHEN #{queryForm.startDate} = #{queryForm.repurchaseDateBegin}
                 AND #{queryForm.endDate} = #{queryForm.repurchaseDateEnd}
            THEN 0  -- 同时间段分析
            ELSE 1  -- 跨时间段分析
        END as is_cross_period
)
```

#### 2.2 跨时间段复购分析 (v2.4 重大修正)
**适用场景**: 付款日期 ≠ 回购日期
**复购定义**: 先找出付款期间的复购客户，再验证这些客户在回购期间是否有购买记录

```sql
-- 跨时间段复购客户识别（v2.4 修正后的正确逻辑）
SELECT 客户唯一编码
FROM (
    -- 第一步：找出付款期间的复购客户（购买天数≥2）
    SELECT tc.客户唯一编码
    FROM TargetCustomers tc
    JOIN lirun.订单明细 om ON tc.客户唯一编码 = om.客户唯一编码
    WHERE [付款期间过滤条件]
    GROUP BY tc.客户唯一编码
    HAVING COUNT(DISTINCT DATE(om.付款时间)) >= 2
) payment_repurchase_customers
WHERE EXISTS (
    -- 第二步：验证这些复购客户在回购期间是否有购买记录（无需再验证复购）
    SELECT 1 FROM lirun.订单明细 om
    WHERE om.客户唯一编码 = payment_repurchase_customers.客户唯一编码
        AND om.付款时间 >= #{queryForm.repurchaseDateBegin}
        AND om.付款时间 < DATE_ADD(#{queryForm.repurchaseDateEnd}, INTERVAL 1 DAY)
        AND [基础过滤条件]
)
```

**业务逻辑说明**:
- 已在付款期间证明复购能力的客户，在回购期间的任何购买都视为有效回购行为
- 避免了v2.3中过于严格的"双重复购"要求
- 更符合实际业务场景和用户预期

**示例**:
- 付款日期：2025-07-01至2025-07-31，回购日期：2025-07-17至2025-07-31
- 付款期间复购客户：52人，跨时间段复购客户：41人
- 客户特征选择"新客"：付款期间14人，跨时间段8人

#### 2.3 同时间段复购分析
**适用场景**: 付款日期 = 回购日期
**复购定义**: 在该时间段内购买次数≥2的客户

```sql
-- 同时间段复购客户识别
SELECT 客户唯一编码
FROM (
    SELECT 客户唯一编码, COUNT(DISTINCT DATE(付款时间)) as purchase_days
    FROM lirun.订单明细 om
    WHERE [基础过滤条件]
    GROUP BY 客户唯一编码
    HAVING purchase_days >= 2
) repurchase_customers
```

**示例**:
- 付款日期：2025-07-01至2025-07-31
- 回购日期：2025-07-01至2025-07-31（相同）
- 结果：目标客户3,122人，复购客户406人，复购率13.00%

### 3. 复购周期计算

```sql
-- 平均复购周期计算
SELECT 
    AVG(DATEDIFF(next_order_date, current_order_date)) as 平均复购周期
FROM (
    SELECT 
        客户唯一编码,
        付款时间 as current_order_date,
        LEAD(付款时间) OVER (
            PARTITION BY 客户唯一编码 
            ORDER BY 付款时间
        ) as next_order_date
    FROM lirun.订单明细
    WHERE [过滤条件]
) t
WHERE next_order_date IS NOT NULL
```

## 📊 主要查询接口

### 1. queryAnalysis - 链接分析统计

**功能**: 提供复购率、复购人数、付款人数等核心指标

**关键CTE结构**:
```sql
-- CTE 1: 基础订单过滤
FilteredOrders AS (...)

-- CTE 1.5: 回购日期期间客户筛选 (v2.1新增)
RepurchasePeriodCustomers AS (...)

-- CTE 2: 实时客户分类
FilteredOrdersWithCustomerType AS (...)

-- CTE 3: 客户汇总数据
CustomerSummary AS (...)

-- CTE 4: 复购客户识别
RepeatCustomers AS (...)

-- CTE 5: 回购日期验证 (v2.1新增)
RepurchaseValidation AS (...)
```

### 2. queryDetail - 复购明细

**功能**: 提供按复购次数分组的详细数据

**输出字段**:
- 复购次数
- 复购人数  
- 复购件数
- 复购金额
- 平均复购周期天数

### 3. downloadOrderDetail - 订单明细导出

**功能**: 导出符合条件的订单明细数据

**输出字段**: 订单的完整明细信息

## 🎯 查询参数

### CustomerLinkQueryForm 参数说明

```java
public class CustomerLinkQueryForm {
    private String customerType;                    // 客户类型: "不限", "新客", "老客"
    private Date startDate;                         // 付款开始日期
    private Date endDate;                           // 付款结束日期
    private String repurchaseDateBegin;             // 回购开始日期
    private String repurchaseDateEnd;               // 回购结束日期
    private List<String> platformGoodsIds;         // 货品ID列表 (v3.0更新)
    private List<String> repurchasePlatformGoodsIds; // 回购ID列表 (v3.0新增)
    private List<String> excludeFlags;             // 排除旗帜列表
}
```

### 参数映射到SQL

```sql
-- 客户类型过滤
<if test="queryForm.customerType == '新客'">
    AND NOT EXISTS (历史购买记录查询)
</if>
<if test="queryForm.customerType == '老客'">  
    AND EXISTS (历史购买记录查询)
</if>

-- 付款时间范围过滤
AND om.付款时间 >= #{queryForm.startDate}
AND om.付款时间 < #{queryForm.endDate}

-- 回购日期验证过滤（v2.1新增）
<if test="queryForm.repurchaseDateBegin != null and queryForm.repurchaseDateEnd != null">
    AND EXISTS (
        SELECT 1 FROM RepurchasePeriodCustomers rpc
        WHERE rpc.客户唯一编码 = om.客户唯一编码
    )
</if>

-- 货品ID过滤
<if test="queryForm.productId != null and queryForm.productId != ''">
    AND om.平台货品ID = #{queryForm.productId}
</if>
```

## ⚡ 性能优化

### 1. 索引建议

```sql
-- lirun.订单明细表建议索引
CREATE INDEX idx_product_payment_time ON 订单明细(平台货品ID, 付款时间);
CREATE INDEX idx_customer_payment_time ON 订单明细(客户唯一编码, 付款时间);
CREATE INDEX idx_payment_time ON 订单明细(付款时间);
```

### 2. 查询优化要点

- 使用CTE分步处理，提高可读性和性能
- EXISTS子查询用于客户分类，避免大表JOIN
- 合理使用索引覆盖查询
- 时间范围查询使用半开区间 `[start, end)`

## 🗑️ 已废弃的表结构

### smart_admin_v3.crm_客户查询 (已删除)
```sql
-- 此表已不再使用，可以安全删除
-- 原用途: 存储预计算的客户汇总数据
-- 废弃原因: 数据同步滞后，改为实时计算
```

### smart_admin_v3.t_customer_classification_log (已删除)  
```sql
-- 此表已不再使用，可以安全删除
-- 原用途: 记录客户分类任务执行日志
-- 废弃原因: 不再需要预计算任务
```

## 🔧 配置文件

### 数据源配置 (sa-base.yaml)
```yaml
spring:
  datasource:
    # 主数据源 - smart_admin_v3
    url: ******************************************
    
    # 外部数据源 - lirun (用于链接视角)
    external:
      url: *********************************
```

### MyBatis映射文件
- 位置: `smart-admin-api-java17-springboot3/sa-admin/src/main/resources/mapper/business/customer/CustomerLinkMapper.xml`
- 包含: queryAnalysis, queryDetail, downloadOrderDetail 三个主要查询

## 📈 数据验证示例

### 验证查询准确性
```sql
-- 验证付款人数
SELECT COUNT(DISTINCT 客户唯一编码) as 付款人数
FROM lirun.订单明细 om
WHERE om.平台货品ID = '779818444472'
    AND om.付款时间 >= '2025-07-23'
    AND om.付款时间 < '2025-07-30'
    AND om.订单状态 != '线下退款'
    AND NOT (om.订单来源 = '手工创建' AND om.订单状态 = '已取消')
    AND om.标记名称 != '贴贴贴贴贴';

-- 验证复购人数  
SELECT COUNT(*) as 复购人数
FROM (
    SELECT 客户唯一编码
    FROM lirun.订单明细 om
    WHERE [同上过滤条件]
    GROUP BY 客户唯一编码
    HAVING COUNT(DISTINCT DATE(om.付款时间)) > 1
) t;
```

## 🚀 部署说明

1. **数据库连接**: 确保应用能同时连接 smart_admin_v3 和 lirun 数据库
2. **索引创建**: 在 lirun.订单明细 表上创建建议的索引
3. **配置更新**: 更新数据源配置文件
4. **代码部署**: 部署包含新查询逻辑的 CustomerLinkMapper.xml

## 📊 字段统计逻辑详解

### 核心统计指标

#### 1. 付款人数 (paymentUserCount)
**定义**: 在指定时间范围内，购买了筛选货品的去重客户数量。

**计算逻辑**:
- 从订单明细表中筛选出符合条件的订单记录
- 应用时间范围过滤：付款时间在查询开始日期和结束日期之间
- 应用货品过滤：平台货品ID在选中的货品列表中
- 应用订单状态过滤：排除线下退款、手工创建已取消、特定标记的订单
- 应用客服标旗过滤：排除用户选择的标旗订单
- 对客户唯一编码进行去重计数

#### 2. 复购人数 (repurchaseUserCount)
**定义**: 在指定时间范围内，对筛选货品有多天购买记录的客户数量。

**计算逻辑**:
- 基于付款人数的筛选条件，进一步分析每个客户的购买行为
- 按客户唯一编码分组，统计每个客户的购买天数
- 购买天数计算方式：对付款时间按日期去重后计数
- 筛选出购买天数大于等于2天的客户
- 对这些客户进行计数得到复购人数

#### 3. 复购率 (repurchaseRate) - v2.2 精度修复
**定义**: 复购客户占总付款客户的百分比，反映客户重复购买的比例。

**计算逻辑**:
- 复购率 = (复购人数 ÷ 付款人数) × 100%
- **重要更新**: 使用截断而非四舍五入，确保计算精度
- 后端: `TRUNCATE(rate, 2)`
- 前端: `Math.floor(rate * 100) / 100`
- 当付款人数为0时，复购率显示为0%

**精度示例**:
- 精确值：42 ÷ 3122 × 100% = 1.34529%
- v2.1 (四舍五入): 1.35%
- v2.2 (截断): 1.34% ✓

#### 3.1 验证复购率 (validatedRepurchaseRate) - v2.1新增
**定义**: 经过回购日期验证的复购率，只统计在指定回购期间确实有购买行为的客户。

**计算逻辑**:
- 验证复购率 = (验证复购人数 ÷ 付款人数) × 100%
- 验证复购人数：在回购日期期间内有购买行为的复购客户数
- 提供更精准的复购率指标，排除在回购期间无活动的客户

#### 4. 平均复购周期 (avgRepurchaseCycle)
**定义**: 复购客户从首次购买到最后一次购买的平均天数，反映客户复购的整体时间跨度。

**计算逻辑**:
- 仅针对复购客户（购买天数≥2）进行计算
- 对每个复购客户，计算其首次购买日期到最后一次购买日期的天数差
- 将所有复购客户的购买周期求平均值
- 结果保留两位小数

#### 5. 平均复购间隔 (avgRepurchaseInterval)
**定义**: 复购客户相邻两次购买之间的平均间隔天数，反映客户复购的频率。

**计算逻辑**:
- 仅针对复购客户的复购行为（排除首次购买）进行计算
- 对每个复购客户，按购买时间排序，计算相邻购买日期的间隔天数
- 将所有复购间隔求平均值
- 结果保留两位小数

#### 6. 最小复购间隔 (minRepurchaseInterval)
**定义**: 所有复购行为中，相邻两次购买间隔的最小天数。

**计算逻辑**:
- 计算所有复购客户的相邻购买间隔
- 取其中的最小值
- 结果为整数天数

#### 7. 最大复购间隔 (maxRepurchaseInterval)
**定义**: 所有复购行为中，相邻两次购买间隔的最大天数。

**计算逻辑**:
- 计算所有复购客户的相邻购买间隔
- 取其中的最大值
- 结果为整数天数

### 复购明细统计指标

#### 8. 复购次数分类 (repurchaseTimes) - v2.4 显示逻辑修复
**定义**: 按客户的复购次数进行分类统计，根据分析类型采用不同逻辑。

**跨时间段分析分类逻辑 (v2.4 修正)**:
- 第1次：在回购期间购买了1天的客户数量
- 第2次：在回购期间购买了2天的客户数量
- 第N次：在回购期间购买了N天的客户数量
- 合计：所有跨时间段复购客户的汇总数据
- 人均：复购数据的人均值

**同时间段分析分类逻辑**:
- 第1次：在该时间段内有2天购买记录的客户（复购1次）
- 第2次：在该时间段内有3天购买记录的客户（复购2次）
- 第N次：在该时间段内有N+1天购买记录的客户（复购N次）
- 合计：所有复购客户的汇总数据
- 人均：复购数据的人均值

**v2.4 重要修复**:
- 修正跨时间段分析的显示逻辑：购买1天显示"第1次"，避免"第0次"的奇怪显示
- 基于回购期间的实际购买天数计算复购次数，更符合业务直觉
- 确保各分组人数之和等于总复购人数，避免重复计算

**显示逻辑对比**:
```sql
-- v2.4 修正后
CASE
    -- 跨时间段分析：购买N天显示第N次
    WHEN rs.is_cross_period = 1 THEN CONCAT('第', rs.total_purchase_times, '次')
    -- 同时间段分析：购买N天显示第N-1次（第1次购买不算复购）
    ELSE CONCAT('第', rs.total_purchase_times - 1, '次')
END as repurchaseTimes
```

#### 9. 各复购次数的人数 (repurchaseCustomers)
**定义**: 每个复购次数分类下的客户数量。

**计算逻辑**:
- 按复购次数分组，统计每组的去重客户数量
- 第0次：统计只有1天购买记录的客户数
- 第N次：统计有N+1天购买记录的客户数
- 合计：所有复购客户的总数

#### 10. 各复购次数的件数 (repurchaseQuantity)
**定义**: 每个复购次数分类下的商品购买件数。

**计算逻辑**:
- 第0次：只有1天购买记录客户的总购买件数
- 第N次：有N+1天购买记录客户的复购件数（排除首次购买件数）
- 合计：所有复购行为的总件数
- 人均：复购总件数除以复购客户数

#### 11. 各复购次数的金额 (repurchaseAmount)
**定义**: 每个复购次数分类下的购买金额。

**计算逻辑**:
- 第0次：只有1天购买记录客户的总购买金额
- 第N次：有N+1天购买记录客户的复购金额（排除首次购买金额）
- 合计：所有复购行为的总金额
- 人均：复购总金额除以复购客户数
- 结果保留两位小数

#### 12. 各复购次数的平均复购周期 (avgRepurchaseCycleDays)
**定义**: 每个复购次数分类下客户的平均复购间隔天数。

**计算逻辑**:
- 第0次：无复购行为，显示为空
- 第N次：该分类下所有复购间隔的平均值
- 合计：所有复购间隔的总平均值
- 人均：与合计相同
- 结果保留两位小数

### 客户分类逻辑

#### 13. 新客老客判断 (客户类型)
**定义**: 基于客户的历史购买记录判断其身份属性。

**判断逻辑**:
- 以查询开始日期为分界点
- 新客：在查询开始日期之前，该客户在系统中没有任何有效购买记录
- 老客：在查询开始日期之前，该客户在系统中已有有效购买记录
- 历史记录查询使用相同的订单过滤条件
- 注意：判断依据是客户的整体购买历史，不限于当前筛选的货品ID

### 数据过滤规则

#### 14. 有效订单定义
**过滤条件**:
- 订单状态不等于"线下退款"
- 排除订单来源为"手工创建"且订单状态为"已取消"的记录
- 排除标记名称为"贴贴贴贴贴"的记录
- 根据用户选择排除特定客服标旗的记录
- 货品ID、客户编码、付款时间等关键字段不能为空

#### 15. 时间范围处理
**处理逻辑**:
- 查询开始日期：包含该日期的00:00:00时刻
- 查询结束日期：不包含该日期，即到前一天的23:59:59时刻
- 使用半开区间设计，便于索引优化和边界处理

#### 16. 购买天数计算
**计算方式**:
- 将付款时间按日期部分去重
- 同一天内的多次购买只计算为1天
- 购买天数 = 去重后的购买日期数量
- 复购次数 = 购买天数 - 1

这些统计逻辑确保了数据的准确性和一致性，为业务分析提供了可靠的数据基础。

## 🔍 回购日期验证功能 (v2.1)

### 功能概述
回购日期验证功能允许用户指定一个回购期间，系统将验证复购客户在该期间内是否确实有购买行为，提供更精准的复购率分析。

### 核心逻辑
1. **双重筛选机制**：
   - 第一层：按付款日期范围筛选订单，识别复购客户
   - 第二层：按回购日期范围验证这些客户是否在指定期间有购买活动

2. **验证复购率计算**：
   - 只统计在回购期间确实有购买行为的复购客户
   - 排除在回购期间无活动的"沉默"客户
   - 提供 `validatedRepurchaseRate` 和 `validatedRepurchaseUserCount` 指标

3. **数据一致性**：
   - 分析统计、复购明细、订单下载三个查询保持一致的筛选逻辑
   - 确保所有相关数据都基于相同的客户范围

### 使用场景
- 验证营销活动期间的实际复购效果
- 分析特定时间段内客户的活跃度
- 提高复购率统计的准确性和业务价值

## � v2.2 重要修复详解

### 问题1: 跨时间段复购分析逻辑错误

**问题描述**:
- 当付款日期≠回购日期时，复购明细只显示极少数客户
- 预期42人复购客户，实际只显示1人
- 数据与复购率分析不一致

**根本原因**:
- 错误地要求复购客户在回购期间必须购买≥2次
- 忽略了跨时间段分析的真实业务含义

**修复方案**:
```sql
-- 修复前（错误）
HAVING MAX(purchase_order) >= 2  -- 要求回购期间购买≥2次

-- 修复后（正确）
HAVING (
    -- 跨时间段分析：所有复购客户都有效
    (cpa.is_cross_period = 1)
    OR
    -- 同时间段分析：只保留购买次数>=2的客户
    (cpa.is_cross_period = 0 AND MAX(purchase_order) >= 2)
)
```

### 问题2: 复购率计算精度问题

**问题描述**:
- 42÷3122×100% = 1.34529%
- 系统显示1.35%（四舍五入）
- 业务要求显示1.34%（截断）

**修复方案**:
```sql
-- 后端修复
-- 修复前
ROUND(rate * 100, 2) as repurchaseRate

-- 修复后
TRUNCATE(rate * 100, 2) as repurchaseRate
```

```javascript
// 前端修复
// 修复前
return rate.toFixed(2);

// 修复后
return (Math.floor(rate * 100) / 100).toFixed(2);
```

### 问题3: SQL语法错误

**问题描述**:
- `Unknown column 'cpa.is_cross_period' in 'having clause'`
- MySQL不支持在HAVING子句中直接引用CROSS JOIN的列

**修复方案**:
```sql
-- 修复前（语法错误）
FROM RepurchaseWithInterval rwi
CROSS JOIN CrossPeriodAnalysis cpa
GROUP BY rwi.客户唯一编码
HAVING (cpa.is_cross_period = 1 OR ...)

-- 修复后（正确）
-- 分离过滤逻辑到WHERE子句
FilteredCustomerTotalPurchases AS (
    SELECT ctp.*
    FROM CustomerTotalPurchases ctp
    CROSS JOIN CrossPeriodAnalysis cpa
    WHERE (cpa.is_cross_period = 1 OR ...)
)
```

### 修复验证

**跨时间段分析测试**:
- 条件：付款日期2025-07-01至2025-07-31，回购日期2025-08-01至2025-08-02
- 修复前：第1次1人，合计1人 ❌
- 修复后：第1次41人，第2次1人，合计42人 ✅

**复购率精度测试**:
- 计算：42÷3122×100% = 1.34529%
- 修复前：1.35% ❌
- 修复后：1.34% ✅

**数据一致性验证**:
- 复购率分析复购人数 = 复购明细合计人数 = 42人 ✅
- 前后端复购率显示一致 = 1.34% ✅

## 🔄 v2.5 重大修复详解

### 问题背景
v2.4版本修复了跨时间段复购分析的业务逻辑，但在复购明细统计中发现了数据重复计算的问题。

### 核心问题分析

#### 问题1: 复购明细数据重复计算
**问题描述**:
- 一个客户有2次复购记录（第1次2000件，第2次700件），总计2700件
- 但在复购明细中，第1次和第2次都显示2700件，导致数据重复
- 合计数据不准确，影响业务分析的准确性

**问题根源**:
```sql
-- 错误的统计逻辑（v2.4及之前）
RepurchaseStats AS (
    SELECT
        rc.复购次数 as 复购次数分组,
        COUNT(CASE WHEN cmr.最大复购次数 >= rc.复购次数 THEN 1 END) as 复购人数,
        SUM(CASE WHEN cmr.最大复购次数 >= rc.复购次数 THEN cmr.客户总复购件数 ELSE 0 END) as 复购件数
        -- 问题：使用客户总复购数据，导致重复计算
    FROM RepurchaseCategories rc
    LEFT JOIN CustomerMaxRepurchase cmr ON cmr.最大复购次数 >= rc.复购次数
)
```

#### 问题2: 前端输入框边框显示不全
**问题描述**:
- 货品ID输入框的右边框被截断，显示不完整
- 影响用户体验和界面美观度

**问题根源**:
- 容器的右边距不足，导致边框被父容器裁剪
- 复杂的CSS样式覆盖导致布局问题

### 修复方案

#### 修复1: 改为按实际复购记录统计
```sql
-- v2.5 修复后的正确逻辑
RepurchaseStats AS (
    SELECT
        CASE
            WHEN frd.复购次数 <= 10 THEN frd.复购次数
            ELSE 999
        END as 复购次数分组,
        COUNT(DISTINCT frd.客户唯一编码) as 复购人数,
        SUM(frd.日订单数量) as 复购件数,  -- 直接统计该次复购的实际数据
        SUM(frd.日订单金额) as 复购金额,
        AVG(frd.复购间隔天数) as 平均复购周期天数
    FROM FilteredRepurchaseData frd
    GROUP BY
        CASE
            WHEN frd.复购次数 <= 10 THEN frd.复购次数
            ELSE 999
        END
)
```

**修复效果**:
- 第1次复购：统计所有客户第1次复购的实际件数和金额
- 第2次复购：统计所有客户第2次复购的实际件数和金额
- 避免了数据重复计算，确保统计准确性

#### 修复2: 简化前端样式修复
```scss
// v2.5 简化的修复方案
&.product-id-item {
    margin-right: 30px; // 适当增加右边距，确保边框显示完整
}
```

**修复原理**:
- 移除复杂的CSS深度选择器和强制样式
- 只调整必要的右边距，让边框有足够的显示空间
- 保持原有的内联样式设置，避免样式冲突

### 修复验证

#### 测试场景1: 复购明细数据准确性
**测试条件**:
- 客户AAG4MHhSABBhUgp9Ix5BW4fw有3天购买记录
- 第1次复购：2000件，2087元
- 第2次复购：700件，730元

**结果对比**:
| 版本 | 第1次复购 | 第2次复购 | 数据准确性 |
|------|----------|----------|-----------|
| v2.4 | 2700件，2817元 | 2700件，2817元 | 重复计算 ❌ |
| v2.5 | **2000件，2087元** | **700件，730元** | **准确统计** ✅ |

#### 测试场景2: 前端边框显示
**测试条件**:
- 货品ID输入框宽度150px
- 包含后缀选择图标

**结果对比**:
| 版本 | 右边框显示 | 样式复杂度 | 用户体验 |
|------|-----------|-----------|----------|
| v2.4 | 被截断 ❌ | 复杂CSS | 影响体验 |
| v2.5 | **完整显示** ✅ | **简化样式** | **良好体验** |

### 业务价值提升

1. **数据准确性**: 复购明细统计完全准确，为业务分析提供可靠数据
2. **用户体验**: 界面显示完整，提升用户操作体验
3. **代码质量**: 简化CSS样式，提高代码可维护性
4. **问题解决思路**: 建立了"先分析再修复"的问题解决模式

### 经验总结

#### 复购明细统计的正确理解
- **按客户去重**: 每个客户只出现在其最大复购次数对应的分组中
- **按记录统计**: 每个复购记录只被计算一次，避免重复
- **业务含义**: 第N次复购 = 所有客户第N次复购行为的汇总

#### 前端样式问题的解决原则
- **简单优先**: 优先使用简单的解决方案
- **分析先行**: 先完整分析代码结构再进行修改
- **避免过度设计**: 不要引入不必要的复杂样式

### 影响范围
- **后端**: CustomerLinkMapper.xml 中的 queryDetail 方法
- **前端**: customer-link.vue 中的货品ID输入框样式
- **数据库**: 无需修改
- **兼容性**: 完全向后兼容

## 🔄 v2.4 重大修复详解

### 问题背景
v2.3版本修复了跨时间段复购分析的逻辑错误，但采用了过于严格的"双重复购"定义，导致业务逻辑不合理。

### 核心问题分析

#### 问题1: 业务逻辑过于严格
**问题描述**:
- v2.3要求客户在付款期间是复购客户，且在回购期间也必须是复购客户
- 导致跨时间段分析结果过少，不符合实际业务需求
- 例如：52个付款期间复购客户，只有2个在回购期间也是复购客户

**业务合理性分析**:
- 已在付款期间证明复购能力的客户，在回购期间的任何购买都应视为有效
- 回购期间时间较短，很多复购客户可能只购买1次，但这不代表失去复购能力
- 不应该对同一批客户在不同时间段重复应用复购标准

#### 问题2: 复购明细显示异常
**问题描述**:
- 跨时间段分析时，复购明细从"第2次"开始显示，缺少"第1次"
- 用户困惑：为什么没有"第1次"的数据？
- 显示逻辑不自然，影响用户体验

### 修复方案

#### 修复1: 调整跨时间段复购定义
```sql
-- v2.3 (过于严格)
WHERE EXISTS (
    SELECT 1 FROM (
        SELECT 客户唯一编码
        FROM lirun.订单明细 om
        WHERE om.客户唯一编码 = payment_repurchase_customers.客户唯一编码
            AND [回购期间过滤条件]
        GROUP BY 客户唯一编码
        HAVING COUNT(DISTINCT DATE(om.付款时间)) >= 2  -- 要求在回购期间也是复购客户
    ) repurchase_in_repurchase_period
)

-- v2.4 (业务合理)
WHERE EXISTS (
    SELECT 1 FROM lirun.订单明细 om
    WHERE om.客户唯一编码 = payment_repurchase_customers.客户唯一编码
        AND [回购期间过滤条件]  -- 只需验证有购买记录即可
)
```

#### 修复2: 调整复购明细显示逻辑
```sql
-- v2.3 (显示异常)
CASE
    WHEN rs.is_cross_period = 1 THEN CONCAT('第', rs.total_purchase_times - 1, '次')
    ELSE CONCAT('第', rs.total_purchase_times - 1, '次')
END as repurchaseTimes

-- v2.4 (显示自然)
CASE
    -- 跨时间段：购买1天显示第1次，购买2天显示第2次
    WHEN rs.is_cross_period = 1 THEN CONCAT('第', rs.total_purchase_times, '次')
    -- 同时间段：购买2天显示第1次（第1次购买不算复购）
    ELSE CONCAT('第', rs.total_purchase_times - 1, '次')
END as repurchaseTimes
```

### 修复验证

#### 测试场景1: 基本跨时间段分析
**条件**:
- 货品ID: 761502031104
- 付款日期: 2025-07-01至2025-07-31
- 回购日期: 2025-07-17至2025-07-31

**结果对比**:
| 版本 | 付款期间复购客户 | 跨时间段复购客户 | 复购明细显示 |
|------|-----------------|-----------------|-------------|
| v2.3 | 52人 | 16人 | 第2次开始 ❌ |
| v2.4 | 52人 | **41人** | **第1次开始** ✅ |

#### 测试场景2: 新客复购分析
**条件**: 在上述条件基础上，客户特征选择"新客"

**结果对比**:
| 版本 | 新客复购客户 | 跨时间段新客复购 | 复购明细分布 |
|------|-------------|-----------------|-------------|
| v2.3 | 14人 | 约2人 | 数据稀少 ❌ |
| v2.4 | 14人 | **8人** | **第1次5人，第2次3人** ✅ |

#### 测试场景3: 下载明细一致性
**验证点**: 下载明细包含的客户范围与复购明细分析一致

**结果**:
- v2.4修复后，下载明细逻辑保持不变（本身就是正确的）
- 复购明细分析与下载明细的客户范围逻辑统一
- 数据一致性得到保证 ✅

### 业务价值提升

1. **数据准确性**: 跨时间段复购客户数从16人提升到41人，更准确反映业务现状
2. **用户体验**: 复购明细从"第1次"开始显示，界面更自然
3. **业务洞察**: 能够正确分析客户在不同时间段的复购行为
4. **决策支持**: 为营销策略和客户运营提供更可靠的数据基础

### 影响范围
- **后端**: CustomerLinkMapper.xml 中的 queryAnalysis 和 queryDetail 方法
- **前端**: 无需修改，自动适配后端逻辑调整
- **数据库**: 无需修改表结构或索引
- **兼容性**: 完全向后兼容，不影响现有功能

## 🎯 v3.0 回购ID精确分析功能详解

### 功能概述
回购ID精确分析功能允许用户在复购分析的基础上，进一步指定特定的回购商品ID，实现更精准的复购行为分析。

### 核心业务逻辑

#### 分析流程
1. **第一步**：在付款日期范围内找出购买了指定货品ID的复购客户
2. **第二步**：在这些复购客户中，筛选出在回购日期范围内购买了指定回购ID的客户
3. **第三步**：计算精确复购率 = (购买了回购ID的客户数 / 付款期间复购客户数) × 100%

#### 业务场景示例
**场景**: 分析购买商品A的复购客户对商品B的复购情况
- **货品ID**: 662928897323 (商品A)
- **回购ID**: 877997159488 (商品B)
- **付款日期**: 2025-07-29至2025-08-04
- **回购日期**: 2025-07-29至2025-08-04

**分析结果**:
- 付款期间购买商品A的复购客户：8人
- 这8人中在回购期间购买商品B的客户：0人
- 精确复购率：0.00% (0/8)

### 技术实现

#### 前端实现 (customer-link.vue)

**1. 回购ID选择组件**
```vue
<a-form-item label="回购ID" class="smart-query-form-item repurchase-product-id-item">
    <a-input
        style="width: 150px; cursor: pointer"
        v-model:value="selectedRepurchaseProductText"
        placeholder="点击选择回购ID"
        readonly
        @click="showRepurchaseProductSelectModal"
    >
        <template #suffix>
            <span style="display: flex; align-items: center; gap: 4px;">
                <CloseOutlined
                    v-if="selectedRepurchaseProducts.length > 0"
                    @click.stop="clearRepurchaseProductSelection"
                    style="cursor: pointer; color: #999; font-size: 12px;"
                    title="清除选择"
                />
                <SelectOutlined />
            </span>
        </template>
    </a-input>
</a-form-item>
```

**2. 回购商品选择逻辑**
```javascript
// 回购商品选择相关变量
const selectedRepurchaseProducts = ref([]);

// 显示回购商品选择弹窗
function showRepurchaseProductSelectModal() {
    repurchaseProductSelectModalRef.value.showModal(queryForm.selectedRepurchaseProductIds);
}

// 回购商品选择确认回调
function onRepurchaseProductSelected(products) {
    selectedRepurchaseProducts.value = products;
    queryForm.selectedRepurchaseProductIds = products.map(p => p.goodsId);
    message.success(`已选择 ${products.length} 个回购ID`);
}
```

**3. API参数传递**
```javascript
const params = {
    platformGoodsIds: queryForm.selectedProductIds,
    repurchasePlatformGoodsIds: queryForm.selectedRepurchaseProductIds, // 新增
    startDate: queryForm.paymentDateBegin,
    endDate: queryForm.paymentDateEnd,
    repurchaseDateBegin: queryForm.repurchaseDateBegin,
    repurchaseDateEnd: queryForm.repurchaseDateEnd,
    customerType: queryForm.customerType,
    excludeFlag: excludeFlags.value.join(','),
};
```

#### 后端实现 (CustomerLinkMapper.xml)

**1. 回购客户筛选逻辑**
```sql
-- RepurchaseCustomers CTE中的回购ID筛选
WHERE EXISTS (
    SELECT 1 FROM lirun.订单明细 om
    WHERE om.客户唯一编码 = payment_repurchase_customers.客户唯一编码
        AND om.平台货品ID IS NOT NULL AND om.平台货品ID != ''
        AND om.订单状态 != '线下退款'
        AND NOT (om.订单来源 = '手工创建' AND om.订单状态 = '已取消')
        AND om.标记名称 != '贴贴贴贴贴'
        <!-- 回购ID精确分析：优先使用回购ID，如果没有则使用货品ID -->
        <choose>
            <when test="queryForm.repurchasePlatformGoodsIds != null and queryForm.repurchasePlatformGoodsIds.size() > 0">
                AND om.平台货品ID IN
                <foreach collection="queryForm.repurchasePlatformGoodsIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <if test="queryForm.platformGoodsIds != null and queryForm.platformGoodsIds.size() > 0">
                    AND om.平台货品ID IN
                    <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            </otherwise>
        </choose>
        AND om.付款时间 >= #{queryForm.repurchaseDateBegin}
        AND om.付款时间 < DATE_ADD(#{queryForm.repurchaseDateEnd}, INTERVAL 1 DAY)
)
```

**2. 复购明细筛选逻辑**
```sql
-- ValidOrders CTE中的回购ID精确分析
<choose>
    <when test="queryForm.repurchasePlatformGoodsIds != null and queryForm.repurchasePlatformGoodsIds.size() > 0">
        <!-- 回购ID精确分析：只显示付款期间复购客户在回购期间购买回购ID的明细 -->
        AND om.平台货品ID IN
        <foreach collection="queryForm.repurchasePlatformGoodsIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <!-- 只包含付款期间的复购客户 -->
        AND EXISTS (
            SELECT 1 FROM lirun.订单明细 om_check
            WHERE om_check.客户唯一编码 = om.客户唯一编码
                AND om_check.平台货品ID IN
                <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                AND om_check.付款时间 >= #{queryForm.startDate}
                AND om_check.付款时间 < DATE_ADD(#{queryForm.endDate}, INTERVAL 1 DAY)
                AND om_check.订单状态 != '线下退款'
                AND NOT (om_check.订单来源 = '手工创建' AND om_check.订单状态 = '已取消')
                AND om_check.标记名称 != '贴贴贴贴贴'
            GROUP BY om_check.客户唯一编码
            HAVING COUNT(DISTINCT DATE(om_check.付款时间)) >= 2
        )
        <!-- 使用回购日期范围 -->
        <if test="queryForm.repurchaseDateBegin != null and queryForm.repurchaseDateBegin != ''">
            AND om.付款时间 >= #{queryForm.repurchaseDateBegin}
        </if>
        <if test="queryForm.repurchaseDateEnd != null and queryForm.repurchaseDateEnd != ''">
            AND om.付款时间 < DATE_ADD(#{queryForm.repurchaseDateEnd}, INTERVAL 1 DAY)
        </if>
    </when>
    <otherwise>
        <!-- 原有逻辑：使用货品ID和付款日期 -->
        <!-- ... 原有逻辑保持不变 ... -->
    </otherwise>
</choose>
```

### UI优化功能

#### 1. 货品ID全部货品支持
**功能**: 支持选择"全部货品"，不限制特定货品ID
**实现**: 使用特殊标识`ALL_PRODUCTS`，后端接收空数组时不添加货品ID筛选条件

#### 2. 独立清除功能
**功能**: 货品ID和回购ID可以单独清除
**实现**: 输入框右侧添加清除按钮，点击可单独清除对应选择

#### 3. 缓存机制
**功能**: 商品选择弹窗增加24小时本地缓存
**实现**:
```javascript
const CACHE_KEY = 'product_select_modal_cache';
const CACHE_EXPIRE_TIME = 24 * 60 * 60 * 1000; // 24小时

function getCachedData() {
    try {
        const cached = localStorage.getItem(CACHE_KEY);
        if (cached) {
            const data = JSON.parse(cached);
            const now = Date.now();
            if (now - data.timestamp < CACHE_EXPIRE_TIME) {
                return data.result;
            } else {
                localStorage.removeItem(CACHE_KEY);
            }
        }
    } catch (e) {
        console.warn('读取缓存失败:', e);
        localStorage.removeItem(CACHE_KEY);
    }
    return null;
}
```

#### 4. 重置逻辑优化
**功能**: 重置按钮清除所有选择，恢复到初始状态
**实现**: 货品ID恢复为"全部货品"，回购ID清空

### 数据验证示例

#### 测试场景1: 相同ID测试
**条件**:
- 货品ID: 662928897323
- 回购ID: 662928897323 (相同)
- 日期范围: 2025-07-29至2025-08-04

**预期结果**: 复购率100% (8/8)

#### 测试场景2: 不同ID测试
**条件**:
- 货品ID: 662928897323
- 回购ID: 877997159488 (不同)
- 日期范围: 2025-07-29至2025-08-04

**预期结果**: 复购率0% (0/8)

#### 测试场景3: 全部货品测试
**条件**:
- 货品ID: 全部货品
- 回购ID: 877997159488
- 日期范围: 2025-07-29至2025-08-04

**预期结果**: 查询所有货品的复购客户对指定回购ID的复购情况

### 开发经验总结

#### 1. 前端组件复用
- 回购ID选择复用了货品ID选择的ProductSelectModal组件
- 通过ref创建独立实例，避免状态冲突
- 保持一致的交互体验

#### 2. 后端SQL逻辑设计
- 使用choose-when-otherwise标签实现条件逻辑
- 优先级：回购ID > 货品ID
- 保持向下兼容，不影响原有功能

#### 3. 数据一致性保证
- 分析数据、复购明细、下载明细三个查询使用一致的筛选逻辑
- 通过EXISTS子查询确保客户范围的一致性

#### 4. 用户体验优化
- 提供清晰的操作反馈
- 支持独立清除选择
- 缓存机制提升性能
- 默认选择降低使用门槛

### 影响范围
- **前端**: customer-link.vue, ProductSelectModal.vue
- **后端**: CustomerLinkQueryForm.java, CustomerLinkMapper.xml
- **数据库**: 无需修改表结构
- **兼容性**: 完全向后兼容

## 📞 联系信息

如有问题，请联系开发团队或查看相关代码文件：
- 前端: `smart-admin-web-javascript/src/views/business/customer/customer-link.vue`
- 后端: `smart-admin-api-java17-springboot3/sa-admin/src/main/java/net/lab1024/sa/admin/module/business/customer/`
- SQL: `CustomerLinkMapper.xml`
