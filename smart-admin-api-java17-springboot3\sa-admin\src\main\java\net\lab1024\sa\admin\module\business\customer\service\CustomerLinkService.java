package net.lab1024.sa.admin.module.business.customer.service;

import net.lab1024.sa.admin.module.business.customer.dao.CustomerLinkDao;
import net.lab1024.sa.admin.module.business.customer.domain.form.CustomerLinkQueryForm;
import net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerLinkAnalysisVO;
import net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerLinkDetailVO;
import net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerLinkOrderDetailVO;
import net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerLinkStatisticsVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.ArrayList;
import java.util.Objects;
import java.util.stream.Collectors;

import jakarta.annotation.Resource;

/**
 * 链接视角 Service
 *
 * <AUTHOR>
 * @Date 2025-01-18 11:00:00
 * @Copyright 1.0
 */
@Service
public class CustomerLinkService {

    @Resource
    private CustomerLinkDao customerLinkDao;

    /**
     * 获取链接分析统计数据
     * @param queryForm 查询参数
     * @return 完整的分析数据
     */
    public CustomerLinkAnalysisVO getAnalysis(CustomerLinkQueryForm queryForm) {
        return customerLinkDao.queryAnalysis(queryForm);
    }

    /**
     * 获取复购明细数据
     * @param queryForm 查询参数
     * @return 复购明细列表
     */
    public List<CustomerLinkDetailVO> getDetail(CustomerLinkQueryForm queryForm) {
        System.out.println("Service开始查询复购明细，参数: " + queryForm);
        List<CustomerLinkDetailVO> details = customerLinkDao.queryDetail(queryForm);
        System.out.println("DAO查询结果: " + (details != null ? details.size() : 0) + " 条");
        if (details == null) {
            details = new ArrayList<>();
        }
        // 过滤掉null元素
        details = details.stream().filter(Objects::nonNull).collect(Collectors.toList());
        System.out.println("过滤null后: " + details.size() + " 条");

        // 数据一致性验证：第0次 + 第1次的人数应该等于总付款人数
        validateDataConsistency(details, queryForm);

        return details;
    }

    /**
     * 验证复购明细数据的一致性
     */
    private void validateDataConsistency(List<CustomerLinkDetailVO> details, CustomerLinkQueryForm queryForm) {
        try {
            CustomerLinkAnalysisVO analysis = customerLinkDao.queryAnalysis(queryForm);
            if (analysis != null && details != null && !details.isEmpty()) {
                // 修复：去掉第0次逻辑，只验证复购明细的总人数与分析数据的复购人数是否一致
                long totalRepurchaseCustomersFromDetail = details.stream()
                    .filter(d -> !"合计".equals(d.getRepurchaseTimes()) && !"人均".equals(d.getRepurchaseTimes()) && !"均值".equals(d.getRepurchaseTimes()))
                    .mapToLong(d -> d.getRepurchaseCustomers() != null ? d.getRepurchaseCustomers() : 0)
                    .sum();

                if (analysis.getRepurchaseUserCount() != null &&
                    Math.abs(totalRepurchaseCustomersFromDetail - analysis.getRepurchaseUserCount()) > 0) {
                    System.err.println("数据一致性警告: 复购明细人数总和(" + totalRepurchaseCustomersFromDetail +
                                     ") 与分析数据复购人数(" + analysis.getRepurchaseUserCount() + ")不一致");
                }
            }
        } catch (Exception e) {
            System.err.println("数据一致性验证失败: " + e.getMessage());
        }
    }

    /**
     * 下载复购明细的订单明细数据
     * @param queryForm 查询参数
     * @return 订单明细列表
     */
    public List<CustomerLinkOrderDetailVO> downloadOrderDetail(CustomerLinkQueryForm queryForm) {
        System.out.println("Service开始查询订单明细，参数: " + queryForm);
        List<CustomerLinkOrderDetailVO> orderDetails = customerLinkDao.downloadOrderDetail(queryForm);
        System.out.println("DAO查询结果: " + (orderDetails != null ? orderDetails.size() : 0) + " 条");
        if (orderDetails == null) {
            orderDetails = new ArrayList<>();
        }
        // 过滤掉null元素
        orderDetails = orderDetails.stream().filter(Objects::nonNull).collect(Collectors.toList());
        System.out.println("过滤null后: " + orderDetails.size() + " 条");
        return orderDetails;
    }


}