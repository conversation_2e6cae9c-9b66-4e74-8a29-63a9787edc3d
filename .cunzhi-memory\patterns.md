# 常用模式和最佳实践

- 客户管理模块代码模式：
1. 前端使用Vue3 Composition API，遵循SmartAdmin标准页面布局规范
2. 查询表单使用smart-query-form类，表格使用TableOperator组件
3. 权限控制使用v-privilege指令和@SaCheckPermission注解
4. 数据查询支持分页、筛选、导出功能
5. 使用中文字段名的VO对象（如客户唯一编码、总销售额等）
6. 后端采用四层架构：Controller->Service->Dao->Mapper
7. 支持多数据源查询（默认数据库和lirun数据库）
- 复购率计算功能实现完成：1)扩展了CustomerLinkQueryForm添加回购日期字段；2)修改了CustomerLinkMapper.xml增加回购日期验证逻辑；3)更新了CustomerLinkAnalysisVO添加验证后复购率字段；4)前端优先显示验证后的复购率和复购人数
- 链接视角页面复购率计算逻辑修复完成：修正了CustomerLinkMapper.xml中queryAnalysis和queryDetail方法的跨时间段复购分析逻辑，将错误的"只验证回购期间购买"改为正确的"先筛选付款期间目标客户，再验证回购期间购买"，解决了缩短回购日期范围时复购人数异常增加的bug
