<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.customer.dao.CustomerLinkDao">

    <select id="queryAnalysis" resultType="net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerLinkAnalysisVO">
        WITH
        -- CTE 1: 筛选付款期间的目标客户（与queryDetail保持一致）
        TargetCustomers AS (
            SELECT DISTINCT 客户唯一编码
            FROM lirun.订单明细 om
            <where>
                om.平台货品ID IS NOT NULL AND om.平台货品ID != ''
                AND om.客户唯一编码 IS NOT NULL AND om.客户唯一编码 != ''
                AND om.付款时间 IS NOT NULL
                AND om.订单状态 != '线下退款'
                AND NOT (om.订单来源 = '手工创建' AND om.订单状态 = '已取消')
                AND om.标记名称 != '贴贴贴贴贴'
                <if test="queryForm.excludeFlag != null and queryForm.excludeFlag != ''">
                    AND (om.客服标旗 IS NULL OR om.客服标旗 NOT IN
                    <foreach collection="queryForm.excludeFlag.split(',')" item="flag" open="(" separator="," close=")">
                        #{flag}
                    </foreach>)
                </if>
                <if test="queryForm.platformGoodsIds != null and queryForm.platformGoodsIds.size() > 0">
                    AND om.平台货品ID IN
                    <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="queryForm.startDate != null and queryForm.startDate != ''">
                    AND om.付款时间 &gt;= #{queryForm.startDate}
                </if>
                <if test="queryForm.endDate != null and queryForm.endDate != ''">
                    AND om.付款时间 &lt; DATE_ADD(#{queryForm.endDate}, INTERVAL 1 DAY)
                </if>
            </where>
        ),

        -- CTE 2: 判断是否为跨时间段复购分析（与queryDetail保持一致）
        CrossPeriodAnalysis AS (
            SELECT
                CASE
                    WHEN #{queryForm.startDate} = #{queryForm.repurchaseDateBegin}
                         AND #{queryForm.endDate} = #{queryForm.repurchaseDateEnd}
                    THEN 0  -- 同时间段分析
                    ELSE 1  -- 跨时间段分析
                END as is_cross_period
        ),

        -- CTE 3: 根据分析类型筛选复购客户（修复跨时间段分析逻辑）
        RepurchaseCustomers AS (
            -- 跨时间段分析：找付款期间的目标客户，然后检查他们在回购期间是否有复购行为
            SELECT tc.客户唯一编码
            FROM TargetCustomers tc
            CROSS JOIN CrossPeriodAnalysis cpa
            WHERE cpa.is_cross_period = 1
                -- 验证该客户在回购期间有复购行为（购买天数≥2天）
                AND (
                    SELECT COUNT(DISTINCT DATE(om.付款时间))
                    FROM lirun.订单明细 om
                    WHERE om.客户唯一编码 = tc.客户唯一编码
                        AND om.平台货品ID IS NOT NULL AND om.平台货品ID != ''
                        AND om.订单状态 != '线下退款'
                        AND NOT (om.订单来源 = '手工创建' AND om.订单状态 = '已取消')
                        AND om.标记名称 != '贴贴贴贴贴'
                        <if test="queryForm.excludeFlag != null and queryForm.excludeFlag != ''">
                            AND (om.客服标旗 IS NULL OR om.客服标旗 NOT IN
                            <foreach collection="queryForm.excludeFlag.split(',')" item="flag" open="(" separator="," close=")">
                                #{flag}
                            </foreach>)
                        </if>
                        <!-- 正确逻辑：回购期间货品ID的选择 -->
                        <choose>
                            <when test="queryForm.repurchasePlatformGoodsIds != null and queryForm.repurchasePlatformGoodsIds.size() > 0">
                                <!-- 情况2：选择了回购ID，检查回购期间是否购买了回购ID -->
                                AND om.平台货品ID IN
                                <foreach collection="queryForm.repurchasePlatformGoodsIds" item="item" open="(" separator="," close=")">
                                    #{item}
                                </foreach>
                            </when>
                            <otherwise>
                                <!-- 情况1：没有选择回购ID，检查回购期间是否购买了付款期间的货品ID -->
                                <if test="queryForm.platformGoodsIds != null and queryForm.platformGoodsIds.size() > 0">
                                    AND om.平台货品ID IN
                                    <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                                        #{item}
                                    </foreach>
                                </if>
                            </otherwise>
                        </choose>
                        <if test="queryForm.repurchaseDateBegin != null and queryForm.repurchaseDateBegin != ''">
                            AND om.付款时间 &gt;= #{queryForm.repurchaseDateBegin}
                        </if>
                        <if test="queryForm.repurchaseDateEnd != null and queryForm.repurchaseDateEnd != ''">
                            AND om.付款时间 &lt; DATE_ADD(#{queryForm.repurchaseDateEnd}, INTERVAL 1 DAY)
                        </if>
                ) &gt;= 2

            UNION

            -- 同时间段分析：在该时间段内购买次数≥2的客户
            SELECT 客户唯一编码
            FROM (
                SELECT
                    tc.客户唯一编码,
                    COUNT(DISTINCT DATE(om.付款时间)) as purchase_days
                FROM TargetCustomers tc
                JOIN lirun.订单明细 om ON tc.客户唯一编码 = om.客户唯一编码
                CROSS JOIN CrossPeriodAnalysis cpa
                WHERE cpa.is_cross_period = 0
                    AND om.平台货品ID IS NOT NULL AND om.平台货品ID != ''
                    AND om.订单状态 != '线下退款'
                    AND NOT (om.订单来源 = '手工创建' AND om.订单状态 = '已取消')
                    AND om.标记名称 != '贴贴贴贴贴'
                    <if test="queryForm.excludeFlag != null and queryForm.excludeFlag != ''">
                        AND (om.客服标旗 IS NULL OR om.客服标旗 NOT IN
                        <foreach collection="queryForm.excludeFlag.split(',')" item="flag" open="(" separator="," close=")">
                            #{flag}
                        </foreach>)
                    </if>
                    <!-- 回购ID精确分析：优先使用回购ID，如果没有则使用货品ID -->
                    <choose>
                        <when test="queryForm.repurchasePlatformGoodsIds != null and queryForm.repurchasePlatformGoodsIds.size() > 0">
                            AND om.平台货品ID IN
                            <foreach collection="queryForm.repurchasePlatformGoodsIds" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        </when>
                        <otherwise>
                            <if test="queryForm.platformGoodsIds != null and queryForm.platformGoodsIds.size() > 0">
                                AND om.平台货品ID IN
                                <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                                    #{item}
                                </foreach>
                            </if>
                        </otherwise>
                    </choose>
                    <if test="queryForm.startDate != null and queryForm.startDate != ''">
                        AND om.付款时间 &gt;= #{queryForm.startDate}
                    </if>
                    <if test="queryForm.endDate != null and queryForm.endDate != ''">
                        AND om.付款时间 &lt; DATE_ADD(#{queryForm.endDate}, INTERVAL 1 DAY)
                    </if>
                GROUP BY tc.客户唯一编码
                HAVING purchase_days &gt;= 2
            ) same_period_repurchase
        ),

        -- CTE 4: 应用客户类型筛选
        FilteredRepurchaseCustomers AS (
            SELECT rc.客户唯一编码
            FROM RepurchaseCustomers rc
            <if test="queryForm.customerType != null and queryForm.customerType != ''">
            WHERE CASE
                WHEN EXISTS (
                    SELECT 1 FROM lirun.订单明细 hist
                    WHERE hist.客户唯一编码 = rc.客户唯一编码
                        AND hist.付款时间 &lt; #{queryForm.startDate}
                        AND hist.订单状态 != '线下退款'
                        AND NOT (hist.订单来源 = '手工创建' AND hist.订单状态 = '已取消')
                        AND hist.标记名称 != '贴贴贴贴贴'
                ) THEN '老客'
                ELSE '新客'
            END = #{queryForm.customerType}
            </if>
        ),

        -- CTE 5: 计算复购周期相关数据（基于回购期间的购买数据）
        RepurchaseCycleData AS (
            SELECT
                frc.客户唯一编码,
                om.付款时间,
                DATE(om.付款时间) as 付款日期
            FROM FilteredRepurchaseCustomers frc
            JOIN lirun.订单明细 om ON frc.客户唯一编码 = om.客户唯一编码
            WHERE om.平台货品ID IS NOT NULL AND om.平台货品ID != ''
                AND om.订单状态 != '线下退款'
                AND NOT (om.订单来源 = '手工创建' AND om.订单状态 = '已取消')
                AND om.标记名称 != '贴贴贴贴贴'
                <!-- 正确逻辑：回购期间货品ID的选择 -->
                <choose>
                    <when test="queryForm.repurchasePlatformGoodsIds != null and queryForm.repurchasePlatformGoodsIds.size() > 0">
                        <!-- 情况2：选择了回购ID，使用回购ID -->
                        AND om.平台货品ID IN
                        <foreach collection="queryForm.repurchasePlatformGoodsIds" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </when>
                    <otherwise>
                        <!-- 情况1：没有选择回购ID，使用付款期间的货品ID -->
                        <if test="queryForm.platformGoodsIds != null and queryForm.platformGoodsIds.size() > 0">
                            AND om.平台货品ID IN
                            <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        </if>
                    </otherwise>
                </choose>
                <if test="queryForm.repurchaseDateBegin != null and queryForm.repurchaseDateBegin != ''">
                    AND om.付款时间 &gt;= #{queryForm.repurchaseDateBegin}
                </if>
                <if test="queryForm.repurchaseDateEnd != null and queryForm.repurchaseDateEnd != ''">
                    AND om.付款时间 &lt; DATE_ADD(#{queryForm.repurchaseDateEnd}, INTERVAL 1 DAY)
                </if>
        ),

        -- CTE 6: 计算购买间隔
        RepurchaseIntervals AS (
            SELECT
                客户唯一编码,
                付款日期,
                LAG(付款日期, 1) OVER (PARTITION BY 客户唯一编码 ORDER BY 付款日期) as previous_date,
                DATEDIFF(付款日期, LAG(付款日期, 1) OVER (PARTITION BY 客户唯一编码 ORDER BY 付款日期)) as interval_days
            FROM (
                SELECT DISTINCT 客户唯一编码, 付款日期
                FROM RepurchaseCycleData
            ) distinct_dates
        ),

        -- CTE 7: 计算间隔统计
        IntervalStats AS (
            SELECT
                客户唯一编码,
                MIN(interval_days) as 客户最小间隔,
                MAX(interval_days) as 客户最大间隔,
                AVG(interval_days) as 客户平均间隔
            FROM RepurchaseIntervals
            WHERE interval_days IS NOT NULL
            GROUP BY 客户唯一编码
        )

        -- 【最终步：聚合结果】（与queryDetail保持一致的复购逻辑）
        SELECT
            -- 目标客户人数：在付款日期范围内购买了指定货品的客户总数
            (SELECT COUNT(*) FROM TargetCustomers) as paymentUserCount,
            -- 复购人数：目标客户中在回购日期范围内也有购买行为的客户数
            (SELECT COUNT(*) FROM FilteredRepurchaseCustomers) as repurchaseUserCount,
            -- 验证后的复购人数（与repurchaseUserCount相同，保持兼容性）
            (SELECT COUNT(*) FROM FilteredRepurchaseCustomers) as validatedRepurchaseUserCount,
            -- 复购率：复购人数 / 目标客户人数 * 100（使用截断而非四舍五入）
            TRUNCATE((SELECT COUNT(*) FROM FilteredRepurchaseCustomers) * 100.0 / (SELECT COUNT(*) FROM TargetCustomers), 2) as repurchaseRate,
            -- 验证后的复购率（与repurchaseRate相同，保持兼容性）
            TRUNCATE((SELECT COUNT(*) FROM FilteredRepurchaseCustomers) * 100.0 / (SELECT COUNT(*) FROM TargetCustomers), 2) as validatedRepurchaseRate,
            -- 平均复购周期：从付款期间首次购买到回购期间最后购买的平均天数
            ROUND(AVG(DATEDIFF(
                (SELECT MAX(DATE(om2.付款时间))
                 FROM lirun.订单明细 om2
                 WHERE om2.客户唯一编码 = frc.客户唯一编码
                   AND om2.平台货品ID IS NOT NULL AND om2.平台货品ID != ''
                   <!-- 正确逻辑：回购期间货品ID的选择 -->
                   <choose>
                       <when test="queryForm.repurchasePlatformGoodsIds != null and queryForm.repurchasePlatformGoodsIds.size() > 0">
                           <!-- 情况2：选择了回购ID，使用回购ID -->
                           AND om2.平台货品ID IN
                           <foreach collection="queryForm.repurchasePlatformGoodsIds" item="item" open="(" separator="," close=")">
                               #{item}
                           </foreach>
                       </when>
                       <otherwise>
                           <!-- 情况1：没有选择回购ID，使用付款期间的货品ID -->
                           <if test="queryForm.platformGoodsIds != null and queryForm.platformGoodsIds.size() > 0">
                               AND om2.平台货品ID IN
                               <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                                   #{item}
                               </foreach>
                           </if>
                       </otherwise>
                   </choose>
                   <if test="queryForm.repurchaseDateBegin != null and queryForm.repurchaseDateBegin != ''">
                       AND om2.付款时间 &gt;= #{queryForm.repurchaseDateBegin}
                   </if>
                   <if test="queryForm.repurchaseDateEnd != null and queryForm.repurchaseDateEnd != ''">
                       AND om2.付款时间 &lt; DATE_ADD(#{queryForm.repurchaseDateEnd}, INTERVAL 1 DAY)
                   </if>
                   AND om2.订单状态 != '线下退款'
                   AND NOT (om2.订单来源 = '手工创建' AND om2.订单状态 = '已取消')
                   AND om2.标记名称 != '贴贴贴贴贴'),
                (SELECT MIN(DATE(om1.付款时间))
                 FROM lirun.订单明细 om1
                 WHERE om1.客户唯一编码 = frc.客户唯一编码
                   AND om1.平台货品ID IS NOT NULL AND om1.平台货品ID != ''
                   <if test="queryForm.platformGoodsIds != null and queryForm.platformGoodsIds.size() > 0">
                       AND om1.平台货品ID IN
                       <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                           #{item}
                       </foreach>
                   </if>
                   <if test="queryForm.startDate != null and queryForm.startDate != ''">
                       AND om1.付款时间 &gt;= #{queryForm.startDate}
                   </if>
                   <if test="queryForm.endDate != null and queryForm.endDate != ''">
                       AND om1.付款时间 &lt; DATE_ADD(#{queryForm.endDate}, INTERVAL 1 DAY)
                   </if>
                   AND om1.订单状态 != '线下退款'
                   AND NOT (om1.订单来源 = '手工创建' AND om1.订单状态 = '已取消')
                   AND om1.标记名称 != '贴贴贴贴贴')
            )), 2) as avgRepurchaseCycle,
            -- 最小、最大、平均复购间隔
            MIN(i.客户最小间隔) as minRepurchaseInterval,
            MAX(i.客户最大间隔) as maxRepurchaseInterval,
            ROUND(AVG(i.客户平均间隔), 2) as avgRepurchaseInterval,
            -- 目标客户人数（重复字段，保持兼容性）
            (SELECT COUNT(*) FROM TargetCustomers) as targetCustomerCount,
            -- 目标复购人数（与repurchaseUserCount相同，保持兼容性）
            (SELECT COUNT(*) FROM FilteredRepurchaseCustomers) as targetRepurchaseUserCount
        FROM FilteredRepurchaseCustomers frc
        LEFT JOIN IntervalStats i ON frc.客户唯一编码 = i.客户唯一编码
    </select>



    <select id="queryDetail" resultType="net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerLinkDetailVO">
        -- 复购明细分析（统一逻辑版本）
        -- 不区分跨时间段和同时间段分析，统一按照实际购买时间顺序计算复购次数
        WITH
        -- CTE 1: 筛选所有有效订单数据
        ValidOrders AS (
            SELECT
                om.客户唯一编码,
                DATE(om.付款时间) as 付款日期,
                SUM(om.数量) as 日订单数量,
                SUM(om.已付) as 日订单金额
            FROM lirun.订单明细 om
            <where>
                om.平台货品ID IS NOT NULL AND om.平台货品ID != ''
                AND om.客户唯一编码 IS NOT NULL AND om.客户唯一编码 != ''
                AND om.付款时间 IS NOT NULL
                AND om.订单状态 != '线下退款'
                AND NOT (om.订单来源 = '手工创建' AND om.订单状态 = '已取消')
                AND om.标记名称 != '贴贴贴贴贴'
                <if test="queryForm.excludeFlag != null and queryForm.excludeFlag != ''">
                    AND (om.客服标旗 IS NULL OR om.客服标旗 NOT IN
                    <foreach collection="queryForm.excludeFlag.split(',')" item="flag" open="(" separator="," close=")">
                        #{flag}
                    </foreach>)
                </if>
                <!-- 回购ID精确分析：当选择了回购ID时，需要特殊处理 -->
                <choose>
                    <when test="queryForm.repurchasePlatformGoodsIds != null and queryForm.repurchasePlatformGoodsIds.size() > 0">
                        <!-- 回购ID精确分析：只显示付款期间复购客户在回购期间购买回购ID的明细 -->
                        AND om.平台货品ID IN
                        <foreach collection="queryForm.repurchasePlatformGoodsIds" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        <!-- 只包含付款期间的复购客户 -->
                        AND EXISTS (
                            SELECT 1 FROM lirun.订单明细 om_check
                            WHERE om_check.客户唯一编码 = om.客户唯一编码
                                AND om_check.平台货品ID IN
                                <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                                    #{item}
                                </foreach>
                                AND om_check.付款时间 >= #{queryForm.startDate}
                                AND om_check.付款时间 &lt; DATE_ADD(#{queryForm.endDate}, INTERVAL 1 DAY)
                                AND om_check.订单状态 != '线下退款'
                                AND NOT (om_check.订单来源 = '手工创建' AND om_check.订单状态 = '已取消')
                                AND om_check.标记名称 != '贴贴贴贴贴'
                            GROUP BY om_check.客户唯一编码
                            HAVING COUNT(DISTINCT DATE(om_check.付款时间)) >= 2
                        )
                        <!-- 使用回购日期范围 -->
                        <if test="queryForm.repurchaseDateBegin != null and queryForm.repurchaseDateBegin != ''">
                            AND om.付款时间 &gt;= #{queryForm.repurchaseDateBegin}
                        </if>
                        <if test="queryForm.repurchaseDateEnd != null and queryForm.repurchaseDateEnd != ''">
                            AND om.付款时间 &lt; DATE_ADD(#{queryForm.repurchaseDateEnd}, INTERVAL 1 DAY)
                        </if>
                    </when>
                    <otherwise>
                        <!-- 修复：复购明细应该显示付款期间目标客户在回购期间的购买明细 -->
                        <!-- 只包含付款期间的目标客户 -->
                        AND EXISTS (
                            SELECT 1 FROM lirun.订单明细 om_check
                            WHERE om_check.客户唯一编码 = om.客户唯一编码
                                <if test="queryForm.platformGoodsIds != null and queryForm.platformGoodsIds.size() > 0">
                                    AND om_check.平台货品ID IN
                                    <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                                        #{item}
                                    </foreach>
                                </if>
                                AND om_check.付款时间 >= #{queryForm.startDate}
                                AND om_check.付款时间 &lt; DATE_ADD(#{queryForm.endDate}, INTERVAL 1 DAY)
                                AND om_check.订单状态 != '线下退款'
                                AND NOT (om_check.订单来源 = '手工创建' AND om_check.订单状态 = '已取消')
                                AND om_check.标记名称 != '贴贴贴贴贴'
                        )
                        <!-- 使用回购日期范围显示明细 -->
                        <if test="queryForm.repurchaseDateBegin != null and queryForm.repurchaseDateBegin != ''">
                            AND om.付款时间 &gt;= #{queryForm.repurchaseDateBegin}
                        </if>
                        <if test="queryForm.repurchaseDateEnd != null and queryForm.repurchaseDateEnd != ''">
                            AND om.付款时间 &lt; DATE_ADD(#{queryForm.repurchaseDateEnd}, INTERVAL 1 DAY)
                        </if>
                    </otherwise>
                </choose>
            </where>
            GROUP BY om.客户唯一编码, DATE(om.付款时间)
        ),

        -- CTE 2: 计算每个客户的购买时间序列和复购次数
        CustomerPurchaseSequence AS (
            SELECT
                vo.客户唯一编码,
                vo.付款日期,
                vo.日订单数量,
                vo.日订单金额,
                ROW_NUMBER() OVER (PARTITION BY vo.客户唯一编码 ORDER BY vo.付款日期) as 购买序号,
                LAG(vo.付款日期, 1) OVER (PARTITION BY vo.客户唯一编码 ORDER BY vo.付款日期) as 上次购买日期
            FROM ValidOrders vo
        ),

        -- CTE 3: 计算复购次数和间隔天数（复购次数 = 购买序号 - 1）
        RepurchaseData AS (
            SELECT
                cps.客户唯一编码,
                cps.付款日期,
                cps.日订单数量,
                cps.日订单金额,
                cps.购买序号 - 1 as 复购次数,  -- 第1次购买不算复购，所以减1
                CASE
                    WHEN cps.上次购买日期 IS NOT NULL
                    THEN DATEDIFF(cps.付款日期, cps.上次购买日期)
                    ELSE NULL
                END as 复购间隔天数
            FROM CustomerPurchaseSequence cps
            WHERE cps.购买序号 &gt; 1  -- 只保留复购记录，排除首次购买
        ),

        -- CTE 4: 应用客户类型筛选
        FilteredRepurchaseData AS (
            SELECT rd.*
            FROM RepurchaseData rd
            <if test="queryForm.customerType != null and queryForm.customerType != ''">
                WHERE CASE
                    WHEN EXISTS (
                        SELECT 1 FROM lirun.订单明细 hist
                        WHERE hist.客户唯一编码 = rd.客户唯一编码
                            AND hist.付款时间 &lt; #{queryForm.startDate}
                            AND hist.订单状态 != '线下退款'
                            AND NOT (hist.订单来源 = '手工创建' AND hist.订单状态 = '已取消')
                            AND hist.标记名称 != '贴贴贴贴贴'
                    ) THEN '老客'
                    ELSE '新客'
                END = #{queryForm.customerType}
            </if>
        ),

        -- CTE 5: 生成固定的复购次数分类（第1次到第10次，第10次以上）
        RepurchaseCategories AS (
            SELECT 1 as 复购次数, '第1次' as 复购次数显示
            UNION ALL SELECT 2, '第2次'
            UNION ALL SELECT 3, '第3次'
            UNION ALL SELECT 4, '第4次'
            UNION ALL SELECT 5, '第5次'
            UNION ALL SELECT 6, '第6次'
            UNION ALL SELECT 7, '第7次'
            UNION ALL SELECT 8, '第8次'
            UNION ALL SELECT 9, '第9次'
            UNION ALL SELECT 10, '第10次'
            UNION ALL SELECT 999, '第10次以上'
        ),

        -- CTE 6: 计算每个客户的最大复购次数和总计数据
        CustomerMaxRepurchase AS (
            SELECT
                frd.客户唯一编码,
                MAX(frd.复购次数) as 最大复购次数,
                SUM(frd.日订单数量) as 客户总复购件数,
                SUM(frd.日订单金额) as 客户总复购金额,
                AVG(frd.复购间隔天数) as 客户平均复购周期天数
            FROM FilteredRepurchaseData frd
            GROUP BY frd.客户唯一编码
        ),

        -- CTE 7: 按实际复购记录统计数据（第N次 = 第N次复购的实际数据）
        RepurchaseStats AS (
            SELECT
                CASE
                    WHEN frd.复购次数 &lt;= 10 THEN frd.复购次数
                    ELSE 999
                END as 复购次数分组,
                COUNT(DISTINCT frd.客户唯一编码) as 复购人数,
                SUM(frd.日订单数量) as 复购件数,
                SUM(frd.日订单金额) as 复购金额,
                AVG(frd.复购间隔天数) as 平均复购周期天数
            FROM FilteredRepurchaseData frd
            GROUP BY
                CASE
                    WHEN frd.复购次数 &lt;= 10 THEN frd.复购次数
                    ELSE 999
                END
        )

        -- 最终查询：使用LEFT JOIN确保所有复购次数分类都显示
        SELECT
            rc.复购次数显示 as repurchaseTimes,
            COALESCE(rs.复购人数, 0) as repurchaseCustomers,
            COALESCE(rs.复购件数, 0) as repurchaseQuantity,
            COALESCE(ROUND(rs.复购金额, 2), 0) as repurchaseAmount,
            CASE
                WHEN COALESCE(rs.复购人数, 0) &gt; 0
                THEN ROUND(rs.复购金额 / rs.复购人数, 2)
                ELSE NULL
            END as unitPrice,
            ROUND(rs.平均复购周期天数, 2) as avgRepurchaseCycleDays
        FROM RepurchaseCategories rc
        LEFT JOIN RepurchaseStats rs ON rc.复购次数 = rs.复购次数分组

        UNION ALL

        -- 合计行：所有复购客户的总计
        SELECT
            '合计' as repurchaseTimes,
            COALESCE(SUM(rs.复购人数), 0) as repurchaseCustomers,
            COALESCE(SUM(rs.复购件数), 0) as repurchaseQuantity,
            COALESCE(ROUND(SUM(rs.复购金额), 2), 0) as repurchaseAmount,
            CASE
                WHEN COALESCE(SUM(rs.复购人数), 0) &gt; 0
                THEN ROUND(SUM(rs.复购金额) / SUM(rs.复购人数), 2)
                ELSE NULL
            END as unitPrice,
            ROUND(AVG(rs.平均复购周期天数), 2) as avgRepurchaseCycleDays
        FROM RepurchaseStats rs

        UNION ALL

        -- 人均行：平均每个复购客户的数据
        SELECT
            '人均' as repurchaseTimes,
            NULL as repurchaseCustomers,
            CASE
                WHEN COALESCE(SUM(rs.复购人数), 0) &gt; 0
                THEN ROUND(SUM(rs.复购件数) / SUM(rs.复购人数), 2)
                ELSE NULL
            END as repurchaseQuantity,
            CASE
                WHEN COALESCE(SUM(rs.复购人数), 0) &gt; 0
                THEN ROUND(SUM(rs.复购金额) / SUM(rs.复购人数), 2)
                ELSE NULL
            END as repurchaseAmount,
            CASE
                WHEN COALESCE(SUM(rs.复购人数), 0) &gt; 0
                THEN ROUND(SUM(rs.复购金额) / SUM(rs.复购人数), 2)
                ELSE NULL
            END as unitPrice,
            ROUND(AVG(rs.平均复购周期天数), 2) as avgRepurchaseCycleDays
        FROM RepurchaseStats rs

        ORDER BY
            CASE
                WHEN repurchaseTimes LIKE '第%次' THEN CAST(SUBSTRING(repurchaseTimes, 2, CHAR_LENGTH(repurchaseTimes) - 2) AS UNSIGNED)
                WHEN repurchaseTimes = '第10次以上' THEN 999
                WHEN repurchaseTimes = '人均' THEN 998
                WHEN repurchaseTimes = '合计' THEN 997
                ELSE 0
            END
    </select>

    <!-- 下载复购明细的订单明细数据 -->
    <select id="downloadOrderDetail" resultType="net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerLinkOrderDetailVO">
        -- 下载明细查询（基于用户提供的SQL逻辑）
        -- 此查询用于数据核对，会列出所有底层订单
        WITH
        -- CTE 1: 筛选基础订单，与主报告逻辑一致（付款日期范围）
        FilteredOrders AS (
            SELECT om.客户唯一编码, om.数量, om.已付, DATE(om.付款时间) as 付款日期
            FROM lirun.订单明细 om
            <where>
                om.平台货品ID IS NOT NULL AND om.平台货品ID != ''
                AND om.客户唯一编码 IS NOT NULL AND om.客户唯一编码 != ''
                AND om.付款时间 IS NOT NULL
                AND om.订单状态 != '线下退款'
                AND NOT (om.订单来源 = '手工创建' AND om.订单状态 = '已取消')
                AND om.标记名称 != '贴贴贴贴贴'
                <if test="queryForm.excludeFlag != null and queryForm.excludeFlag != ''">
                    AND (om.客服标旗 IS NULL OR om.客服标旗 NOT IN
                    <foreach collection="queryForm.excludeFlag.split(',')" item="flag" open="(" separator="," close=")">
                        #{flag}
                    </foreach>)
                </if>
                <if test="queryForm.platformGoodsIds != null and queryForm.platformGoodsIds.size() > 0">
                    AND om.平台货品ID IN
                    <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="queryForm.startDate != null and queryForm.startDate != ''">
                    AND om.付款时间 &gt;= #{queryForm.startDate}
                </if>
                <if test="queryForm.endDate != null and queryForm.endDate != ''">
                    AND om.付款时间 &lt; DATE_ADD(#{queryForm.endDate}, INTERVAL 1 DAY)
                </if>
            </where>
        ),

        -- CTE 1.5: 回购日期期间内的客户筛选 - 只保留在回购日期期间内有购买行为的客户
        RepurchasePeriodCustomers AS (
            SELECT DISTINCT 客户唯一编码
            FROM lirun.订单明细 repurchase_order
            WHERE 1=1
            <!-- 回购ID精确分析：优先使用回购ID，如果没有则使用货品ID -->
            <choose>
                <when test="queryForm.repurchasePlatformGoodsIds != null and queryForm.repurchasePlatformGoodsIds.size() > 0">
                    AND repurchase_order.平台货品ID IN
                    <foreach collection="queryForm.repurchasePlatformGoodsIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    <if test="queryForm.platformGoodsIds != null and queryForm.platformGoodsIds.size() > 0">
                        AND repurchase_order.平台货品ID IN
                        <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                </otherwise>
            </choose>
            <if test="queryForm.repurchaseDateBegin != null and queryForm.repurchaseDateBegin != ''">
                AND repurchase_order.付款时间 &gt;= #{queryForm.repurchaseDateBegin}
            </if>
            <if test="queryForm.repurchaseDateEnd != null and queryForm.repurchaseDateEnd != ''">
                AND repurchase_order.付款时间 &lt; DATE_ADD(#{queryForm.repurchaseDateEnd}, INTERVAL 1 DAY)
            </if>
            AND repurchase_order.订单状态 != '线下退款'
            AND NOT (repurchase_order.订单来源 = '手工创建' AND repurchase_order.订单状态 = '已取消')
            AND repurchase_order.标记名称 != '贴贴贴贴贴'
            <if test="queryForm.excludeFlag != null and queryForm.excludeFlag != ''">
                AND (repurchase_order.客服标旗 IS NULL OR repurchase_order.客服标旗 NOT IN
                <foreach collection="queryForm.excludeFlag.split(',')" item="flag" open="(" separator="," close=")">
                    #{flag}
                </foreach>)
            </if>
        ),

        -- CTE 2: 基于lirun数据库实时计算客户分类，并应用回购日期筛选
        FilteredOrdersWithCustomerType AS (
            SELECT
                fo.客户唯一编码,
                fo.数量,
                fo.已付,
                fo.付款日期,
                CASE
                    WHEN EXISTS (
                        SELECT 1 FROM lirun.订单明细 hist
                        WHERE hist.客户唯一编码 = fo.客户唯一编码
                            AND hist.付款时间 &lt; #{queryForm.startDate}
                            AND hist.订单状态 != '线下退款'
                            AND NOT (hist.订单来源 = '手工创建' AND hist.订单状态 = '已取消')
                            AND hist.标记名称 != '贴贴贴贴贴'
                    ) THEN '老客'
                    ELSE '新客'
                END as 客户类型
            FROM FilteredOrders fo
            -- 只保留在回购日期期间内有购买行为的客户
            WHERE EXISTS (
                SELECT 1 FROM RepurchasePeriodCustomers rpc
                WHERE rpc.客户唯一编码 = fo.客户唯一编码
            )
            <if test="queryForm.customerType != null and queryForm.customerType != ''">
                AND CASE
                    WHEN EXISTS (
                        SELECT 1 FROM lirun.订单明细 hist
                        WHERE hist.客户唯一编码 = fo.客户唯一编码
                            AND hist.付款时间 &lt; #{queryForm.startDate}
                            AND hist.订单状态 != '线下退款'
                            AND NOT (hist.订单来源 = '手工创建' AND hist.订单状态 = '已取消')
                            AND hist.标记名称 != '贴贴贴贴贴'
                    ) THEN '老客'
                    ELSE '新客'
                END = #{queryForm.customerType}
            </if>
        ),
        -- CTE 2: 按天聚合，计算每日的购买事件
        DailyAggregatedPurchases AS (
            SELECT 客户唯一编码, 付款日期
            FROM FilteredOrdersWithCustomerType GROUP BY 客户唯一编码, 付款日期
        ),
        -- CTE 3: 计算每个客户的购买次序和总购买次数
        CustomerPurchaseStats AS (
            SELECT
                客户唯一编码,
                付款日期,
                ROW_NUMBER() OVER (PARTITION BY 客户唯一编码 ORDER BY 付款日期) as purchase_order,
                COUNT(*) OVER (PARTITION BY 客户唯一编码) as total_purchases
            FROM DailyAggregatedPurchases
        )
        -- 最终查询：将分析维度关联回原始的订单明细表
        SELECT
            -- 分析维度
            CASE
                WHEN cps.purchase_order = 1 THEN '首次购买'
                ELSE '复购'
            END as repurchaseTimes,
            cps.total_purchases as purchaseOrder,
            cps.purchase_order as repurchaseCycleDays,
            -- 原始订单明细
            od.客户唯一编码 as customerUniqueCode,
            od.原始单号 as originalOrderNo,
            od.平台货品ID as platformGoodsId,
            od.商家编码 as merchantCode,
            od.数量 as quantity,
            od.已付 as paidAmount,
            od.付款时间 as paymentTime,
            od.店铺名称 as shopName
        FROM
            lirun.订单明细 od
        JOIN
            CustomerPurchaseStats cps ON od.客户唯一编码 = cps.客户唯一编码 AND DATE(od.付款时间) = cps.付款日期
        <where>
            -- 再次应用核心过滤条件，确保关联的准确性
            od.订单状态 != '线下退款'
            AND NOT (od.订单来源 = '手工创建' AND od.订单状态 = '已取消')
            AND od.标记名称 != '贴贴贴贴贴'
            <if test="queryForm.platformGoodsIds != null and queryForm.platformGoodsIds.size() > 0">
                AND od.平台货品ID IN
                <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="queryForm.startDate != null and queryForm.startDate != ''">
                AND od.付款时间 &gt;= #{queryForm.startDate}
            </if>
            <if test="queryForm.endDate != null and queryForm.endDate != ''">
                AND od.付款时间 &lt; DATE_ADD(#{queryForm.endDate}, INTERVAL 1 DAY)
            </if>
            <if test="queryForm.excludeFlag != null and queryForm.excludeFlag != ''">
                AND (od.客服标旗 IS NULL OR od.客服标旗 NOT IN
                <foreach collection="queryForm.excludeFlag.split(',')" item="flag" open="(" separator="," close=")">
                    #{flag}
                </foreach>)
            </if>
        </where>
        ORDER BY
            od.客户唯一编码,
            od.付款时间
    </select>

</mapper>